# 导航模型微调配置文件
# 基于 Qwen2.5-VL-3B 模型的视觉导航任务微调

# ==================== 数据配置 ====================
data:
  data_root: "data/finetune" # 训练数据根目录
  split_file: "data/finetune/split.json" # 数据集分割文件
  max_length: 2048 # 最大序列长度（增加以支持更长对话）
  image_size: [224, 224] # 图像尺寸

# ==================== 模型配置 ====================
model:
  model_name: "data/Qwen2.5-VL-3B-Instruct" # 预训练模型路径
  use_4bit: true # 使用4bit量化节省显存
  use_lora: true # 使用LoRA微调
  lora_rank: 32 # LoRA rank（增加以提高性能）
  lora_alpha: 64 # LoRA alpha（增加以提高性能）
  lora_dropout: 0.1 # LoRA dropout
  target_modules: # LoRA目标模块
    - "q_proj"
    - "k_proj"
    - "v_proj"
    - "o_proj"
    - "gate_proj"
    - "up_proj"
    - "down_proj"

# ==================== 训练配置 ====================
training:
  output_dir: "./checkpoints" # 输出目录（改为checkpoints）
  num_train_epochs: 3 # 训练轮数
  per_device_train_batch_size: 6 # 每设备训练批次大小（优化显存使用）
  per_device_eval_batch_size: 4 # 每设备验证批次大小
  gradient_accumulation_steps: 2 # 梯度累积步数
  learning_rate: 2e-4 # 学习率
  weight_decay: 0.01 # 权重衰减
  warmup_ratio: 0.03 # 预热比例
  lr_scheduler_type: "cosine" # 学习率调度器
  logging_steps: 50 # 日志记录步数
  save_steps: 500 # 保存检查点步数
  eval_steps: 500 # 验证步数
  save_total_limit: 3 # 保留检查点数量
  evaluation_strategy: "steps" # 验证策略
  save_strategy: "steps" # 保存策略
  load_best_model_at_end: true # 加载最佳模型
  metric_for_best_model: "eval_loss" # 最佳模型指标
  greater_is_better: false # 指标越小越好
  bf16: true # 使用bfloat16精度
  gradient_checkpointing: true # 梯度检查点节省显存
  dataloader_pin_memory: false # 禁用内存固定（避免多进程问题）
  remove_unused_columns: false # 保留所有列
  ddp_find_unused_parameters: false # 优化多GPU训练
  dataloader_num_workers: 0 # 禁用多进程（避免pickle问题）

# ==================== 导航Token配置 ====================
navigation_tokens:
  - "<move_forward>" # 前进动作
  - "<turn_left>" # 左转动作
  - "<turn_right>" # 右转动作

# ==================== 日志配置 ====================
logging:
  use_swanlab: false # 是否使用SwanLab
  swanlab_project: "navigation-finetune" # SwanLab项目名
  use_wandb: false # 是否使用WandB
  wandb_project: "navigation-finetune" # WandB项目名
  run_name: null # 运行名称（自动生成）
  log_level: "info" # 日志级别
  disable_tqdm: false # 是否禁用进度条

# ==================== 推理配置 ====================
inference:
  max_new_tokens: 50 # 最大生成token数
  do_sample: false # 是否采样（贪心解码）
  temperature: 1.0 # 温度参数
  top_p: 1.0 # Top-p采样
  return_full_text: false # 是否返回完整文本

# ==================== 硬件配置 ====================
hardware:
  device_map: "auto" # 设备映射策略
  torch_dtype: "bfloat16" # 数据类型
  attn_implementation: "eager" # 注意力实现（避免flash attention问题）
