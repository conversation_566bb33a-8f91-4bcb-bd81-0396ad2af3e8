# 导航模型微调配置文件

# 数据配置
data:
  data_root: "data/finetune"
  split_file: "data/finetune/split.json"
  max_length: 512
  image_size: [224, 224]

# 模型配置
model:
  model_name: "data/Qwen2.5-VL-3B-Instruct"
  use_4bit: true
  use_lora: true
  lora_rank: 16
  lora_alpha: 32
  lora_dropout: 0.1
  target_modules:
    - "q_proj"
    - "k_proj"
    - "v_proj"
    - "o_proj"
    - "gate_proj"
    - "up_proj"
    - "down_proj"

# 训练配置
training:
  output_dir: "./results"
  num_train_epochs: 3
  per_device_train_batch_size: 4 # 增加批次大小，利用24GB显存
  per_device_eval_batch_size: 4
  gradient_accumulation_steps: 4 # 减少梯度累积步数，因为增加了批次大小
  learning_rate: 2e-4
  weight_decay: 0.01
  warmup_ratio: 0.1
  logging_steps: 10
  save_steps: 200 # 更频繁保存，避免训练中断丢失进度
  eval_steps: 200
  save_total_limit: 5 # 保留更多检查点
  evaluation_strategy: "steps"
  save_strategy: "steps"
  load_best_model_at_end: true
  metric_for_best_model: "eval_loss"
  greater_is_better: false
  fp16: false
  bf16: true
  gradient_checkpointing: true
  dataloader_pin_memory: true # 启用内存固定，加速数据传输
  remove_unused_columns: false
  ddp_find_unused_parameters: false # 优化多GPU训练
  dataloader_num_workers: 4 # 增加数据加载工作进程

# 特殊Token配置
navigation_tokens:
  - "<move_forward>"
  - "<turn_left>"
  - "<turn_right>"

# 日志配置
logging:
  use_swanlab: false # 使用SwanLab替代wandb
  swanlab_project: "navigation-finetune"
  use_wandb: false # 向后兼容，实际使用SwanLab
  wandb_project: "navigation-finetune" # 向后兼容
  run_name: null
  log_level: "info"
  disable_tqdm: false

# 推理配置
inference:
  max_new_tokens: 50
  do_sample: false
  temperature: 1.0
  top_p: 1.0
  return_full_text: false

# 硬件配置
hardware:
  device_map: "auto"
  torch_dtype: "bfloat16"
  attn_implementation: "flash_attention_2" # 如果支持的话
