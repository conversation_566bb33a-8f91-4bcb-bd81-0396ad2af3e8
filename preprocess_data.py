#!/usr/bin/env python3
"""
数据预处理脚本
将原始数据转换为训练格式并保存，避免重复转换
"""

import os
import sys
import json
import pickle
import argparse
import logging
from pathlib import Path
from tqdm import tqdm
import torch

# 添加src目录到Python路径
sys.path.append("src")

# 设置日志
logging.basicConfig(
    level=logging.INFO, format="%(asctime)s - %(name)s - %(levelname)s - %(message)s"
)
logger = logging.getLogger(__name__)


def preprocess_and_save_data(args):
    """预处理数据并保存"""

    logger.info("开始数据预处理...")

    try:
        # 导入必要的库
        from transformers import Qwen2VLProcessor
        from data_processor import NavigationDataset

        # 1. 加载processor
        logger.info(f"加载processor: {args.model_name}")
        processor = Qwen2VLProcessor.from_pretrained(args.model_name)
        logger.info("✅ Processor加载成功")

        # 2. 加载原始数据集
        logger.info("加载原始数据集...")
        dataset = NavigationDataset(
            data_root=args.data_root,
            split_file=args.split_file,
            split="train",
            processor=None,  # 不使用processor，我们手动处理
            max_length=args.max_length,
        )

        total_samples = len(dataset)
        logger.info(f"原始数据集大小: {total_samples}")

        # 限制样本数量（如果指定）
        if args.max_samples > 0:
            total_samples = min(args.max_samples, total_samples)
            logger.info(f"限制处理样本数量为: {total_samples}")

        # 3. 转换数据格式函数
        def convert_to_unsloth_format(sample):
            """转换为Unsloth期望的格式"""
            try:
                # 构建对话格式
                messages = [
                    {
                        "role": "user",
                        "content": [
                            {"type": "image", "image": sample["image"]},
                            {"type": "text", "text": sample["input_text"]},
                        ],
                    },
                    {
                        "role": "assistant",
                        "content": [
                            {"type": "text", "text": sample["target"]},
                        ],
                    },
                ]
                return {"messages": messages}
            except Exception as e:
                logger.warning(f"转换样本失败: {e}")
                return None

        # 4. 批量处理数据
        logger.info("开始批量转换数据...")
        processed_data = []
        failed_count = 0

        # 使用tqdm显示进度条
        for i in tqdm(range(total_samples), desc="转换数据"):
            try:
                # 获取原始样本
                sample = dataset[i]

                # 转换格式
                converted_sample = convert_to_unsloth_format(sample)

                if converted_sample is not None:
                    processed_data.append(converted_sample)
                else:
                    failed_count += 1

            except Exception as e:
                logger.warning(f"处理样本 {i} 失败: {e}")
                failed_count += 1
                continue

        successful_count = len(processed_data)
        logger.info(f"✅ 数据转换完成")
        logger.info(f"成功转换: {successful_count} 个样本")
        logger.info(f"失败样本: {failed_count} 个")
        logger.info(
            f"成功率: {successful_count/(successful_count+failed_count)*100:.2f}%"
        )

        # 5. 保存预处理数据
        output_file = args.output_file
        logger.info(f"保存预处理数据到: {output_file}")

        # 创建输出目录
        os.makedirs(os.path.dirname(output_file), exist_ok=True)

        # 保存为pickle格式（更快）
        with open(output_file, "wb") as f:
            pickle.dump(processed_data, f)

        # 保存样本统计信息（JSON格式）
        sample_info = {
            "sample_count": len(processed_data),
            "first_sample_keys": (
                list(processed_data[0].keys()) if processed_data else []
            ),
            "sample_structure": "messages format for Unsloth training",
        }
        json_file = output_file.replace(".pkl", "_info.json")
        logger.info(f"保存样本信息到: {json_file}")
        with open(json_file, "w", encoding="utf-8") as f:
            json.dump(sample_info, f, ensure_ascii=False, indent=2)

        # 6. 保存元数据
        metadata = {
            "total_samples": successful_count,
            "failed_samples": failed_count,
            "success_rate": successful_count / (successful_count + failed_count) * 100,
            "max_length": args.max_length,
            "model_name": args.model_name,
            "data_root": args.data_root,
            "split_file": args.split_file,
        }

        metadata_file = output_file.replace(".pkl", "_metadata.json")
        with open(metadata_file, "w", encoding="utf-8") as f:
            json.dump(metadata, f, ensure_ascii=False, indent=2)

        logger.info(f"✅ 元数据保存到: {metadata_file}")

        # 7. 显示文件大小
        file_size = os.path.getsize(output_file) / 1024 / 1024  # MB
        logger.info(f"预处理数据文件大小: {file_size:.2f} MB")

        logger.info("🎉 数据预处理完成！")
        return True

    except Exception as e:
        logger.error(f"数据预处理失败: {e}")
        import traceback

        traceback.print_exc()
        return False


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="预处理训练数据")

    # 输入参数
    parser.add_argument(
        "--data_root", type=str, default="data/finetune", help="数据根目录"
    )
    parser.add_argument(
        "--split_file",
        type=str,
        default="data/finetune/split.json",
        help="数据分割文件",
    )
    parser.add_argument(
        "--model_name",
        type=str,
        default="data/Qwen2.5-VL-3B-Instruct",
        help="模型名称或路径",
    )

    # 输出参数
    parser.add_argument(
        "--output_file",
        type=str,
        default="data/preprocessed/train_data.pkl",
        help="预处理数据输出文件",
    )
    parser.add_argument(
        "--max_samples", type=int, default=-1, help="最大处理样本数，-1表示处理全部"
    )
    parser.add_argument("--max_length", type=int, default=2048, help="最大序列长度")

    # 处理参数
    parser.add_argument("--batch_size", type=int, default=1000, help="批处理大小")
    parser.add_argument("--num_workers", type=int, default=4, help="并行处理数量")

    args = parser.parse_args()

    logger.info("数据预处理参数:")
    for key, value in vars(args).items():
        logger.info(f"  {key}: {value}")

    # 执行预处理
    success = preprocess_and_save_data(args)

    if success:
        logger.info("✅ 预处理成功完成")
        return 0
    else:
        logger.error("❌ 预处理失败")
        return 1


if __name__ == "__main__":
    sys.exit(main())
