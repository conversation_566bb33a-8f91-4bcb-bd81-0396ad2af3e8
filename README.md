# 🧭 视觉导航模型微调项目

基于 **Qwen2.5-VL-3B** 模型的视觉导航任务微调实现，支持多 GPU 训练和高效推理。

![微调框图](assets/image.png)

## 📁 项目结构

```
finetune/
├── 📂 src/                       # 核心源代码模块
│   ├── __init__.py              # 包初始化文件
│   ├── data_processor.py        # 数据预处理和加载
│   ├── model_config.py          # 模型配置和初始化
│   ├── trainer.py               # 训练循环和评估
│   ├── inference.py             # 推理和预测
│   ├── token_utils.py           # 特殊Token处理
│   └── qwen_data_utils.py       # Qwen数据工具
├── 📂 scripts/                  # 训练脚本
│   └── run_multi_gpu_training.sh # 多GPU训练启动脚本
├── 📂 data/                     # 数据目录
│   ├── finetune/                # 微调数据集（HM3D + Matterport）
│   ├── preprocessed/            # 预处理数据
│   └── Qwen2.5-VL-3B-Instruct/ # 预训练模型
├── 📂 checkpoints/              # 模型检查点
├── 📂 logs/                     # 训练日志
├── 📂 assets/                   # 项目资源
├── 🐍 train.py                  # 主训练脚本
├── 🐍 train_multi_gpu.py        # 多GPU训练脚本
├── 🐍 inference_demo.py         # 推理演示脚本
├── 🐍 preprocess_data.py        # 数据预处理脚本
├── ⚙️ config.yaml               # 主配置文件
├── ⚙️ accelerate_config.yaml    # 多GPU配置
└── 📖 README.md                 # 项目说明
```

## 🎯 项目特点

- **🚀 高效训练**: 支持 LoRA 微调和 4bit 量化，显存友好
- **🔥 多 GPU 支持**: 8 卡并行训练，充分利用硬件资源
- **📊 智能数据处理**: 自动处理 HM3D 和 Matterport 数据集
- **🎮 导航任务**: 专门针对视觉导航任务优化
- **⚡ 快速推理**: 优化的推理流程，支持实时导航

## 🚀 快速开始

### 1. 环境准备

```bash
# 激活conda环境
conda activate ./.conda

# 安装依赖（如果尚未安装）
pip install torch torchvision transformers accelerate peft bitsandbytes datasets pillow pyyaml tqdm swanlab
```

### 2. 数据准备

数据集包含 HM3D 和 Matterport 两个 3D 场景数据集：

```
data/finetune/
├── HM3D/                        # HM3D数据集
│   ├── scene1/
│   │   ├── *.json              # 包含问题答案对和导航动作序列
│   │   └── *.jpg               # 导航图像
│   └── scene2/
└── Matterport/                  # Matterport数据集
    ├── scene1/
    └── scene2/
```

每个 JSON 文件包含：

- **问题答案对**: 导航任务的问题和最终答案
- **导航动作序列**: 包含`move_forward`、`turn_left`、`turn_right`等动作
- **图像路径**: 对应每个动作的视觉观察

### 3. 配置调整

编辑 `config.yaml` 文件，根据硬件配置调整参数：

```yaml
# 关键配置项
training:
  per_device_train_batch_size: 6 # 根据显存调整（RTX 3090建议6）
  gradient_accumulation_steps: 2 # 梯度累积
  learning_rate: 2e-4 # 学习率
  num_train_epochs: 3 # 训练轮数

model:
  lora_rank: 32 # LoRA rank（提高性能）
  lora_alpha: 64 # LoRA alpha
  use_4bit: true # 4bit量化节省显存
```

### 4. 开始训练

#### 单 GPU 训练

```bash
python train.py
```

#### 多 GPU 训练（推荐 8 卡）

```bash
# 首次配置accelerate
accelerate config

# 启动8卡训练
bash scripts/run_multi_gpu_training.sh
```

### 5. 推理测试

```bash
python inference_demo.py
```

## 🔧 核心功能

### 📊 数据处理

- **自动数据加载**: 支持 HM3D 和 Matterport 数据集格式
- **智能预处理**: 图像和文本的联合处理
- **特殊 Token 处理**: 自动添加导航动作 Token
- **缓存机制**: 预处理数据缓存，避免重复计算

### 🤖 模型配置

- **LoRA 微调**: 高效的参数微调，只训练 1%参数
- **4bit 量化**: 显存优化，支持更大批次训练
- **多 GPU 支持**: 分布式训练，充分利用 8 张 RTX 3090
- **梯度检查点**: 进一步节省显存使用

### 🏃‍♂️ 训练优化

- **混合精度**: bfloat16 训练，提升训练速度
- **梯度累积**: 模拟更大批次训练效果
- **学习率调度**: Cosine 退火策略
- **早停机制**: 防止过拟合

### 🎯 推理引擎

- **快速推理**: 优化的推理流程
- **导航动作解析**: 自动解析特殊 Token
- **批量处理**: 支持批量图像处理

## ⚙️ 配置说明

### 数据配置

| 参数         | 说明         | 默认值                     |
| ------------ | ------------ | -------------------------- |
| `data_root`  | 数据根目录   | `data/finetune`            |
| `max_length` | 最大序列长度 | `2048`                     |
| `image_size` | 图像尺寸     | `[224, 224]`               |
| `split_file` | 数据分割文件 | `data/finetune/split.json` |

### 模型配置

| 参数         | 说明           | 默认值                        |
| ------------ | -------------- | ----------------------------- |
| `model_name` | 预训练模型路径 | `data/Qwen2.5-VL-3B-Instruct` |
| `use_4bit`   | 4bit 量化      | `true`                        |
| `lora_rank`  | LoRA rank      | `32`                          |
| `lora_alpha` | LoRA alpha     | `64`                          |

### 训练配置

| 参数                          | 说明           | 默认值 |
| ----------------------------- | -------------- | ------ |
| `per_device_train_batch_size` | 每设备批次大小 | `6`    |
| `gradient_accumulation_steps` | 梯度累积步数   | `2`    |
| `learning_rate`               | 学习率         | `2e-4` |
| `num_train_epochs`            | 训练轮数       | `3`    |

## 🔍 性能优化

### 显存优化策略

- ✅ **4bit 量化**: 节省 50%显存，支持更大模型
- ✅ **梯度检查点**: 节省 30%显存，略微降低速度
- ✅ **LoRA 微调**: 只训练 1%参数，大幅减少显存需求
- ✅ **混合精度**: bfloat16 训练，提升速度并节省显存

### 训练加速技术

- ✅ **多 GPU 并行**: 8 卡训练，线性加速
- ✅ **数据预处理**: 避免重复计算，提升数据加载速度
- ✅ **优化数据加载**: 减少 I/O 瓶颈
- ✅ **编译优化**: 支持 torch.compile 加速

### 性能基准（8x RTX 3090）

| 指标       | 数值           |
| ---------- | -------------- |
| 总批次大小 | 96 (6×2×8)     |
| 训练速度   | ~2.5 steps/sec |
| 显存使用   | ~20GB per GPU  |
| 训练时间   | ~4 小时/epoch  |
| 推理速度   | ~100ms/image   |

## 🐛 故障排除

### 常见问题及解决方案

#### 1. CUDA 内存不足

```yaml
# 解决方案：减少批次大小
training:
  per_device_train_batch_size: 4 # 从6改为4
  gradient_accumulation_steps: 4 # 从2改为4
  gradient_checkpointing: true # 启用梯度检查点
```

#### 2. 数据加载错误

```bash
# 检查数据路径和格式
ls data/finetune/HM3D/
ls data/finetune/Matterport/

# 验证JSON格式
python -c "import json; json.load(open('data/finetune/split.json'))"
```

#### 3. 模型加载失败

```bash
# 检查模型文件完整性
ls data/Qwen2.5-VL-3B-Instruct/

# 验证transformers版本兼容性
pip show transformers
```

#### 4. 多 GPU 训练问题

```bash
# 重新配置accelerate
accelerate config

# 检查GPU状态
nvidia-smi

# 设置环境变量
export CUDA_VISIBLE_DEVICES=0,1,2,3,4,5,6,7
```

## 📚 使用示例

### 训练示例

```python
# 导入核心模块
from src import NavigationDataset, ModelConfig, NavigationTrainer

# 创建数据集
dataset = NavigationDataset(
    data_root="data/finetune",
    split_file="data/finetune/split.json",
    split="train"
)

# 配置模型
config = ModelConfig(
    model_name="data/Qwen2.5-VL-3B-Instruct",
    use_lora=True,
    lora_rank=32
)

# 开始训练
trainer = NavigationTrainer(model, dataset)
trainer.train()
```

### 推理示例

```python
from src import NavigationInference
from PIL import Image

# 初始化推理引擎
inference = NavigationInference("checkpoints/best_model")

# 加载图像
image = Image.open("path/to/navigation_image.jpg")
question = "Where is the bathroom?"

# 执行推理
result = inference.predict(image, question)
print(f"导航动作: {result}")
```

## 🤝 贡献指南

1. Fork 项目
2. 创建功能分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 打开 Pull Request

## 📄 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情。

## 🙏 致谢

- [Qwen2.5-VL](https://github.com/QwenLM/Qwen2-VL) - 基础视觉语言模型
- [Transformers](https://github.com/huggingface/transformers) - 模型框架
- [PEFT](https://github.com/huggingface/peft) - 参数高效微调
- [Accelerate](https://github.com/huggingface/accelerate) - 分布式训练

---

<div align="center">
  <strong>🌟 如果这个项目对您有帮助，请给个 Star！ 🌟</strong>
</div>
