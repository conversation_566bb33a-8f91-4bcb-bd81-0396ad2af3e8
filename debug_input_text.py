#!/usr/bin/env python3
"""
调试输入文本内容
"""

import sys
sys.path.append("src")

from data_processor import NavigationDataset

def debug_input_text():
    """调试输入文本内容"""
    print("=" * 50)
    print("调试输入文本内容")
    print("=" * 50)
    
    try:
        # 创建数据集（不使用processor）
        dataset = NavigationDataset(
            data_root="data/finetune",
            split_file="data/finetune/split.json",
            split="train",
            processor=None,
            max_length=512
        )
        
        print(f"数据集大小: {len(dataset)}")
        
        if len(dataset) > 0:
            # 检查前几个样本
            for i in range(min(5, len(dataset))):
                sample = dataset[i]
                print(f"\n样本 {i}:")
                print(f"输入文本: '{sample['input_text']}'")
                print(f"目标: '{sample['target']}'")
                print(f"动作: '{sample['action']}'")
                print(f"问题: '{sample['question']}'")
                print(f"答案: '{sample['answer']}'")
                
                # 检查是否包含图像token
                if '<image>' in sample['input_text']:
                    print("✓ 包含 <image> token")
                else:
                    print("✗ 不包含 <image> token")
        
        return True
        
    except Exception as e:
        print(f"调试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    debug_input_text()
