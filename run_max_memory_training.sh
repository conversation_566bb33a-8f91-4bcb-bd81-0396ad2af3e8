#!/bin/bash

# 最大内存利用率训练脚本
# 充分占满所有显存，最大化训练效率

echo "🔥 开始最大内存利用率训练..."
echo "目标：充分占满8张RTX 3090的所有显存"

# GPU信息
nvidia-smi --query-gpu=index,name,memory.total,memory.free --format=csv,noheader,nounits

# 环境变量优化
export CUDA_VISIBLE_DEVICES=0,1,2,3,4,5,6,7
export NCCL_DEBUG=WARN  # 减少日志输出
export NCCL_SOCKET_IFNAME=^docker0,lo
export NCCL_IB_DISABLE=1
export NCCL_P2P_DISABLE=0
export NCCL_TREE_THRESHOLD=0

# 内存优化设置
export PYTORCH_CUDA_ALLOC_CONF=max_split_size_mb:256,roundup_power2_divisions:16
export CUDA_LAUNCH_BLOCKING=0

# 训练参数
DATA_ROOT="data/finetune"
SPLIT_FILE="data/finetune/split.json"
MODEL_NAME="data/Qwen2.5-VL-3B-Instruct"
OUTPUT_DIR="results_max_memory"

# 激进的批次大小设置
# 目标：尽可能占满显存
PER_DEVICE_BATCH_SIZE=8   # 每卡8个样本
GRADIENT_ACCUMULATION=2   # 梯度累积2步
MAX_LENGTH=1536          # 稍微减少序列长度以容纳更大批次

# 总有效批次大小 = 8 * 2 * 8 = 128
TOTAL_BATCH_SIZE=$((PER_DEVICE_BATCH_SIZE * GRADIENT_ACCUMULATION * 8))

echo "🎯 激进训练配置:"
echo "- 每设备批次大小: $PER_DEVICE_BATCH_SIZE"
echo "- 梯度累积步数: $GRADIENT_ACCUMULATION"
echo "- 最大序列长度: $MAX_LENGTH"
echo "- 总有效批次大小: $TOTAL_BATCH_SIZE"
echo "- 预计每卡显存使用: ~22GB"

# 创建输出目录
mkdir -p "$OUTPUT_DIR"

# 启动训练
echo "🚀 启动最大内存利用率训练..."

accelerate launch \
    --config_file accelerate_config.yaml \
    --main_process_port 29501 \
    train_multi_gpu.py \
    --data_root "$DATA_ROOT" \
    --split_file "$SPLIT_FILE" \
    --model_name "$MODEL_NAME" \
    --output_dir "$OUTPUT_DIR" \
    --num_train_epochs 1 \
    --per_device_train_batch_size $PER_DEVICE_BATCH_SIZE \
    --gradient_accumulation_steps $GRADIENT_ACCUMULATION \
    --learning_rate 1e-4 \
    --max_length $MAX_LENGTH \
    --save_steps 500 \
    --logging_steps 20 \
    --max_samples -1

echo "✅ 最大内存利用率训练完成！"
echo "检查最终显存使用情况:"
nvidia-smi --query-gpu=index,memory.used,memory.total --format=csv,noheader,nounits
