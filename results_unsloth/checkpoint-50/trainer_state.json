{"best_global_step": null, "best_metric": null, "best_model_checkpoint": null, "epoch": 3.126984126984127, "eval_steps": 500, "global_step": 50, "is_hyper_param_search": false, "is_local_process_zero": true, "is_world_process_zero": true, "log_history": [{"epoch": 0.06349206349206349, "grad_norm": 3.9430017471313477, "learning_rate": 0.0, "loss": 6.4283, "step": 1}, {"epoch": 0.12698412698412698, "grad_norm": 3.902428388595581, "learning_rate": 4e-05, "loss": 6.4359, "step": 2}, {"epoch": 0.19047619047619047, "grad_norm": 3.9600038528442383, "learning_rate": 8e-05, "loss": 6.397, "step": 3}, {"epoch": 0.25396825396825395, "grad_norm": 4.2864532470703125, "learning_rate": 0.00012, "loss": 6.2481, "step": 4}, {"epoch": 0.31746031746031744, "grad_norm": 4.888348579406738, "learning_rate": 0.00016, "loss": 5.6297, "step": 5}, {"epoch": 0.38095238095238093, "grad_norm": 5.253284454345703, "learning_rate": 0.0002, "loss": 4.5459, "step": 6}, {"epoch": 0.4444444444444444, "grad_norm": 4.650755882263184, "learning_rate": 0.00019555555555555556, "loss": 3.5236, "step": 7}, {"epoch": 0.5079365079365079, "grad_norm": 4.539042949676514, "learning_rate": 0.00019111111111111114, "loss": 2.9929, "step": 8}, {"epoch": 0.5714285714285714, "grad_norm": 4.8299880027771, "learning_rate": 0.0001866666666666667, "loss": 2.5017, "step": 9}, {"epoch": 0.6349206349206349, "grad_norm": 4.564659595489502, "learning_rate": 0.00018222222222222224, "loss": 2.0696, "step": 10}, {"epoch": 0.6984126984126984, "grad_norm": 4.576236248016357, "learning_rate": 0.00017777777777777779, "loss": 1.7755, "step": 11}, {"epoch": 0.7619047619047619, "grad_norm": 4.534637451171875, "learning_rate": 0.00017333333333333334, "loss": 1.4947, "step": 12}, {"epoch": 0.8253968253968254, "grad_norm": 4.548929214477539, "learning_rate": 0.00016888888888888889, "loss": 1.2294, "step": 13}, {"epoch": 0.8888888888888888, "grad_norm": 4.064885139465332, "learning_rate": 0.00016444444444444444, "loss": 0.9969, "step": 14}, {"epoch": 0.9523809523809523, "grad_norm": 2.076901912689209, "learning_rate": 0.00016, "loss": 0.7445, "step": 15}, {"epoch": 1.0, "grad_norm": 1.0838782787322998, "learning_rate": 0.00015555555555555556, "loss": 0.6087, "step": 16}, {"epoch": 1.0634920634920635, "grad_norm": 0.6713747382164001, "learning_rate": 0.0001511111111111111, "loss": 0.4933, "step": 17}, {"epoch": 1.126984126984127, "grad_norm": 0.4584672749042511, "learning_rate": 0.00014666666666666666, "loss": 0.438, "step": 18}, {"epoch": 1.1904761904761905, "grad_norm": 0.33644261956214905, "learning_rate": 0.00014222222222222224, "loss": 0.3814, "step": 19}, {"epoch": 1.253968253968254, "grad_norm": 0.3130214512348175, "learning_rate": 0.0001377777777777778, "loss": 0.3492, "step": 20}, {"epoch": 1.3174603174603174, "grad_norm": 0.46031445264816284, "learning_rate": 0.00013333333333333334, "loss": 0.3466, "step": 21}, {"epoch": 1.380952380952381, "grad_norm": 0.2745843529701233, "learning_rate": 0.00012888888888888892, "loss": 0.2698, "step": 22}, {"epoch": 1.4444444444444444, "grad_norm": 0.47510045766830444, "learning_rate": 0.00012444444444444444, "loss": 0.2987, "step": 23}, {"epoch": 1.507936507936508, "grad_norm": 0.47609633207321167, "learning_rate": 0.00012, "loss": 0.2409, "step": 24}, {"epoch": 1.5714285714285714, "grad_norm": 0.2664735019207001, "learning_rate": 0.00011555555555555555, "loss": 0.2339, "step": 25}, {"epoch": 1.6349206349206349, "grad_norm": 0.3094956576824188, "learning_rate": 0.00011111111111111112, "loss": 0.2143, "step": 26}, {"epoch": 1.6984126984126984, "grad_norm": 0.39057326316833496, "learning_rate": 0.00010666666666666667, "loss": 0.2308, "step": 27}, {"epoch": 1.7619047619047619, "grad_norm": 0.203495055437088, "learning_rate": 0.00010222222222222222, "loss": 0.2138, "step": 28}, {"epoch": 1.8253968253968254, "grad_norm": 0.19461150467395782, "learning_rate": 9.777777777777778e-05, "loss": 0.1833, "step": 29}, {"epoch": 1.8888888888888888, "grad_norm": 0.32211199402809143, "learning_rate": 9.333333333333334e-05, "loss": 0.2625, "step": 30}, {"epoch": 1.9523809523809523, "grad_norm": 0.20727941393852234, "learning_rate": 8.888888888888889e-05, "loss": 0.1786, "step": 31}, {"epoch": 2.0, "grad_norm": 0.29012131690979004, "learning_rate": 8.444444444444444e-05, "loss": 0.1612, "step": 32}, {"epoch": 2.0634920634920633, "grad_norm": 0.1883278340101242, "learning_rate": 8e-05, "loss": 0.1919, "step": 33}, {"epoch": 2.126984126984127, "grad_norm": 0.20890922844409943, "learning_rate": 7.555555555555556e-05, "loss": 0.1791, "step": 34}, {"epoch": 2.1904761904761907, "grad_norm": 0.2613193094730377, "learning_rate": 7.111111111111112e-05, "loss": 0.1715, "step": 35}, {"epoch": 2.253968253968254, "grad_norm": 0.1828531175851822, "learning_rate": 6.666666666666667e-05, "loss": 0.1547, "step": 36}, {"epoch": 2.317460317460317, "grad_norm": 0.15171293914318085, "learning_rate": 6.222222222222222e-05, "loss": 0.1555, "step": 37}, {"epoch": 2.380952380952381, "grad_norm": 0.1635979861021042, "learning_rate": 5.7777777777777776e-05, "loss": 0.1778, "step": 38}, {"epoch": 2.4444444444444446, "grad_norm": 0.200700044631958, "learning_rate": 5.333333333333333e-05, "loss": 0.1912, "step": 39}, {"epoch": 2.507936507936508, "grad_norm": 0.1870204508304596, "learning_rate": 4.888888888888889e-05, "loss": 0.185, "step": 40}, {"epoch": 2.571428571428571, "grad_norm": 0.16596756875514984, "learning_rate": 4.4444444444444447e-05, "loss": 0.162, "step": 41}, {"epoch": 2.634920634920635, "grad_norm": 0.17354214191436768, "learning_rate": 4e-05, "loss": 0.1708, "step": 42}, {"epoch": 2.6984126984126986, "grad_norm": 0.16863597929477692, "learning_rate": 3.555555555555556e-05, "loss": 0.1704, "step": 43}, {"epoch": 2.761904761904762, "grad_norm": 0.17005296051502228, "learning_rate": 3.111111111111111e-05, "loss": 0.1788, "step": 44}, {"epoch": 2.825396825396825, "grad_norm": 0.13263869285583496, "learning_rate": 2.6666666666666667e-05, "loss": 0.1604, "step": 45}, {"epoch": 2.888888888888889, "grad_norm": 0.16746854782104492, "learning_rate": 2.2222222222222223e-05, "loss": 0.1674, "step": 46}, {"epoch": 2.9523809523809526, "grad_norm": 0.14936323463916779, "learning_rate": 1.777777777777778e-05, "loss": 0.1497, "step": 47}, {"epoch": 3.0, "grad_norm": 0.19621454179286957, "learning_rate": 1.3333333333333333e-05, "loss": 0.1418, "step": 48}, {"epoch": 3.0634920634920633, "grad_norm": 0.13079145550727844, "learning_rate": 8.88888888888889e-06, "loss": 0.1373, "step": 49}, {"epoch": 3.126984126984127, "grad_norm": 0.16931311786174774, "learning_rate": 4.444444444444445e-06, "loss": 0.1345, "step": 50}], "logging_steps": 1, "max_steps": 50, "num_input_tokens_seen": 0, "num_train_epochs": 4, "save_steps": 500, "stateful_callbacks": {"TrainerControl": {"args": {"should_epoch_stop": false, "should_evaluate": false, "should_log": false, "should_save": true, "should_training_stop": true}, "attributes": {}}}, "total_flos": 6694133480423424.0, "train_batch_size": 16, "trial_name": null, "trial_params": null}