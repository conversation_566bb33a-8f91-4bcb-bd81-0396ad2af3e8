{"_valid_kwargs_names": ["do_convert_rgb", "do_resize", "size", "size_divisor", "default_to_square", "resample", "do_rescale", "rescale_factor", "do_normalize", "image_mean", "image_std", "do_pad", "do_center_crop", "crop_size", "data_format", "input_data_format", "device", "min_pixels", "max_pixels", "patch_size", "temporal_patch_size", "merge_size"], "crop_size": null, "data_format": "channels_first", "default_to_square": true, "device": null, "do_center_crop": null, "do_convert_rgb": true, "do_normalize": true, "do_pad": null, "do_rescale": true, "do_resize": true, "image_mean": [0.48145466, 0.4578275, 0.40821073], "image_processor_type": "Qwen2VLImageProcessor", "image_std": [0.26862954, 0.26130258, 0.27577711], "input_data_format": null, "max_pixels": 12845056, "merge_size": 2, "min_pixels": 3136, "model_valid_processing_keys": ["do_convert_rgb", "do_resize", "size", "size_divisor", "default_to_square", "resample", "do_rescale", "rescale_factor", "do_normalize", "image_mean", "image_std", "do_pad", "do_center_crop", "crop_size", "data_format", "input_data_format", "device", "min_pixels", "max_pixels", "patch_size", "temporal_patch_size", "merge_size"], "patch_size": 14, "processor_class": "Qwen2_5_VLProcessor", "resample": 3, "rescale_factor": 0.00392156862745098, "size": {"longest_edge": 12845056, "shortest_edge": 3136}, "size_divisor": null, "temporal_patch_size": 2, "video_processor_type": "Qwen2VLVideoProcessor"}