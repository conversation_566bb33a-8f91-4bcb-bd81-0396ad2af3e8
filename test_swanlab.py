"""
测试SwanLab集成
验证SwanLab是否正确安装和配置
"""

import sys
import os

# 添加src目录到Python路径
sys.path.append('src')

def test_swanlab_import():
    """测试SwanLab导入"""
    print("="*50)
    print("测试SwanLab导入")
    print("="*50)
    
    try:
        import swanlab
        print("✓ SwanLab导入成功")
        print(f"SwanLab版本: {swanlab.__version__}")
        return True
    except ImportError as e:
        print(f"✗ SwanLab导入失败: {e}")
        print("请确保已安装SwanLab: pip install swanlab")
        return False


def test_swanlab_login():
    """测试SwanLab登录状态"""
    print("="*50)
    print("测试SwanLab登录状态")
    print("="*50)
    
    try:
        import swanlab
        
        # 尝试初始化一个测试项目
        run = swanlab.init(
            project="test-project",
            experiment_name="test-run",
            config={"test": True}
        )
        
        # 记录一个测试指标
        swanlab.log({"test_metric": 0.5})
        
        # 完成运行
        swanlab.finish()
        
        print("✓ SwanLab登录和初始化成功")
        return True
        
    except Exception as e:
        print(f"✗ SwanLab登录或初始化失败: {e}")
        print("请确保已登录SwanLab: swanlab login")
        return False


def test_trainer_integration():
    """测试训练器集成"""
    print("="*50)
    print("测试训练器SwanLab集成")
    print("="*50)
    
    try:
        from trainer import SwanLabCallback, SWANLAB_AVAILABLE
        
        if not SWANLAB_AVAILABLE:
            print("✗ SwanLab在训练器中不可用")
            return False
        
        # 创建回调实例
        callback = SwanLabCallback()
        print("✓ SwanLab回调创建成功")
        
        # 测试回调方法
        print("✓ SwanLab训练器集成正常")
        return True
        
    except Exception as e:
        print(f"✗ 训练器集成测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_training_args():
    """测试训练参数"""
    print("="*50)
    print("测试训练参数配置")
    print("="*50)
    
    try:
        import argparse
        
        # 模拟命令行参数
        parser = argparse.ArgumentParser()
        parser.add_argument("--use_swanlab", action="store_true", default=True)
        parser.add_argument("--swanlab_project", type=str, default="test-navigation")
        parser.add_argument("--run_name", type=str, default="test-run")
        
        args = parser.parse_args([])
        args.use_swanlab = True
        
        print(f"✓ SwanLab参数配置: use_swanlab={args.use_swanlab}")
        print(f"✓ 项目名称: {args.swanlab_project}")
        print(f"✓ 运行名称: {args.run_name}")
        
        return True
        
    except Exception as e:
        print(f"✗ 训练参数测试失败: {e}")
        return False


def main():
    """主测试函数"""
    print("开始SwanLab集成测试...")
    
    tests = [
        ("SwanLab导入", test_swanlab_import),
        ("SwanLab登录", test_swanlab_login),
        ("训练器集成", test_trainer_integration),
        ("训练参数", test_training_args)
    ]
    
    results = []
    
    for test_name, test_func in tests:
        print(f"\n正在运行: {test_name}")
        try:
            result = test_func()
            results.append((test_name, result))
            status = "通过" if result else "失败"
            print(f"{test_name}: {status}")
        except Exception as e:
            print(f"{test_name}: 异常 - {e}")
            results.append((test_name, False))
        
        print("-" * 50)
    
    # 总结
    print("\n" + "="*50)
    print("SwanLab集成测试总结")
    print("="*50)
    
    passed = sum(1 for _, result in results if result)
    total = len(results)
    
    for test_name, result in results:
        status = "✓" if result else "✗"
        print(f"{status} {test_name}")
    
    print(f"\n总计: {passed}/{total} 测试通过")
    
    if passed == total:
        print("🎉 SwanLab集成测试全部通过！")
        print("现在可以使用 --use_swanlab 参数启动训练")
    else:
        print("⚠️  有些测试失败，请检查SwanLab配置")
        
        if passed == 0:
            print("\n建议步骤:")
            print("1. 安装SwanLab: pip install swanlab")
            print("2. 登录SwanLab: swanlab login")
            print("3. 重新运行此测试")


if __name__ == "__main__":
    main()
