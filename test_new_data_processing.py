#!/usr/bin/env python3
"""
测试新的数据处理方式
"""

import sys
sys.path.append("src")

from data_processor import NavigationDataset
from token_utils import NavigationTokenizer

def test_new_data_processing():
    """测试新的数据处理方式"""
    print("=" * 50)
    print("测试新的数据处理方式")
    print("=" * 50)
    
    try:
        # 初始化tokenizer和processor
        nav_tokenizer = NavigationTokenizer("data/Qwen2.5-VL-3B-Instruct")
        processor = nav_tokenizer.setup_processor()
        
        # 创建数据集（使用processor）
        dataset = NavigationDataset(
            data_root="data/finetune",
            split_file="data/finetune/split.json",
            split="train",
            processor=processor,
            max_length=512
        )
        
        print(f"数据集大小: {len(dataset)}")
        
        if len(dataset) > 0:
            # 测试第一个样本
            print("\n处理第一个样本...")
            sample = dataset[0]
            
            print("样本键:", list(sample.keys()))
            
            # 检查tensor形状
            for key, value in sample.items():
                if hasattr(value, 'shape'):
                    print(f"{key}: shape={value.shape}, dtype={value.dtype}")
                elif isinstance(value, str):
                    print(f"{key}: '{value}'")
                else:
                    print(f"{key}: {value}")
            
            # 检查是否有image_grid_thw
            if "image_grid_thw" in sample:
                print(f"\n✓ 样本包含image_grid_thw: {sample['image_grid_thw']}")
            else:
                print("\n✗ 样本不包含image_grid_thw")
            
            # 解码input_ids看看实际的文本内容
            if "input_ids" in sample:
                decoded_text = processor.tokenizer.decode(sample["input_ids"], skip_special_tokens=False)
                print(f"\n解码后的输入文本:\n{decoded_text}")
                
                # 检查是否包含图像相关的token
                if "<|vision_start|>" in decoded_text and "<|vision_end|>" in decoded_text:
                    print("✓ 包含视觉token")
                else:
                    print("✗ 不包含视觉token")
        
        return True
        
    except Exception as e:
        print(f"测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    test_new_data_processing()
