#!/usr/bin/env python3
"""
优化的 Unsloth 训练脚本
使用预处理的数据，避免重复转换，大幅提升启动速度
"""

import os
import sys
import argparse
import logging
import pickle
import json
from pathlib import Path

# 添加src目录到Python路径
sys.path.append("src")

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def load_preprocessed_data(data_file):
    """加载预处理的数据"""
    logger.info(f"加载预处理数据: {data_file}")
    
    if not os.path.exists(data_file):
        raise FileNotFoundError(f"预处理数据文件不存在: {data_file}")
    
    # 加载数据
    with open(data_file, 'rb') as f:
        data = pickle.load(f)
    
    logger.info(f"✅ 成功加载 {len(data)} 个预处理样本")
    
    # 加载元数据（如果存在）
    metadata_file = data_file.replace('.pkl', '_metadata.json')
    if os.path.exists(metadata_file):
        with open(metadata_file, 'r', encoding='utf-8') as f:
            metadata = json.load(f)
        logger.info(f"数据集元数据: {metadata}")
    
    return data

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="优化的 Unsloth 训练")
    
    # 数据参数
    parser.add_argument("--preprocessed_data", type=str, default="data/preprocessed/train_data.pkl",
                       help="预处理数据文件路径")
    parser.add_argument("--model_name", type=str, default="data/Qwen2.5-VL-3B-Instruct",
                       help="模型名称或路径")
    
    # 训练参数
    parser.add_argument("--output_dir", type=str, default="results_unsloth_optimized",
                       help="输出目录")
    parser.add_argument("--num_train_epochs", type=int, default=1,
                       help="训练轮数")
    parser.add_argument("--per_device_train_batch_size", type=int, default=8,
                       help="每设备训练批次大小")
    parser.add_argument("--gradient_accumulation_steps", type=int, default=2,
                       help="梯度累积步数")
    parser.add_argument("--learning_rate", type=float, default=2e-4,
                       help="学习率")
    parser.add_argument("--max_steps", type=int, default=-1,
                       help="最大训练步数，-1表示使用全部数据")
    parser.add_argument("--max_length", type=int, default=2048,
                       help="最大序列长度")
    parser.add_argument("--save_steps", type=int, default=1000,
                       help="保存检查点的步数")
    parser.add_argument("--logging_steps", type=int, default=50,
                       help="日志记录步数")
    parser.add_argument("--max_samples", type=int, default=-1,
                       help="最大训练样本数，-1表示使用全部数据")
    
    args = parser.parse_args()
    
    logger.info("开始优化的 Unsloth 训练...")
    logger.info(f"参数: {args}")
    
    try:
        # 导入 Unsloth
        from unsloth import FastVisionModel, is_bf16_supported
        from unsloth.trainer import UnslothVisionDataCollator
        from trl import SFTTrainer, SFTConfig
        import torch
        
        logger.info("✅ Unsloth 导入成功")
        
        # 1. 加载预处理数据
        dataset = load_preprocessed_data(args.preprocessed_data)
        
        # 限制样本数量（如果指定）
        if args.max_samples > 0:
            dataset = dataset[:args.max_samples]
            logger.info(f"限制训练样本数量为: {args.max_samples}")
        
        logger.info(f"最终训练数据集大小: {len(dataset)}")
        
        # 2. 加载模型和tokenizer
        logger.info(f"加载模型: {args.model_name}")
        model, tokenizer = FastVisionModel.from_pretrained(
            model_name=args.model_name,
            max_seq_length=args.max_length,
            dtype=None,  # 自动选择
            load_in_4bit=True,  # 使用4bit量化节省内存
        )
        
        logger.info("✅ 模型加载成功")
        
        # 3. 添加LoRA适配器
        model = FastVisionModel.get_peft_model(
            model,
            r=32,  # 增加LoRA rank以提高性能
            target_modules=["q_proj", "k_proj", "v_proj", "o_proj",
                          "gate_proj", "up_proj", "down_proj"],
            lora_alpha=64,  # 增加alpha
            lora_dropout=0,  # 支持优化
            bias="none",     # 支持优化
            use_gradient_checkpointing="unsloth",  # 使用Unsloth的梯度检查点
            random_state=3407,
            use_rslora=False,  # 支持排名稳定的LoRA
            loftq_config=None, # LoftQ
        )
        
        logger.info("✅ LoRA适配器添加成功")
        
        # 4. 设置训练器
        logger.info("设置训练器...")
        
        # 启用训练模式
        FastVisionModel.for_training(model)
        
        # 计算训练步数
        total_batch_size = args.per_device_train_batch_size * args.gradient_accumulation_steps
        num_steps_per_epoch = len(dataset) // total_batch_size
        total_steps = num_steps_per_epoch * args.num_train_epochs
        
        if args.max_steps > 0:
            total_steps = min(total_steps, args.max_steps)
        
        logger.info(f"训练配置:")
        logger.info(f"  每设备批次大小: {args.per_device_train_batch_size}")
        logger.info(f"  梯度累积步数: {args.gradient_accumulation_steps}")
        logger.info(f"  总有效批次大小: {total_batch_size}")
        logger.info(f"  每轮步数: {num_steps_per_epoch}")
        logger.info(f"  总训练步数: {total_steps}")
        
        trainer = SFTTrainer(
            model=model,
            tokenizer=tokenizer,
            data_collator=UnslothVisionDataCollator(model, tokenizer),  # 必须使用！
            train_dataset=dataset,
            args=SFTConfig(
                per_device_train_batch_size=args.per_device_train_batch_size,
                gradient_accumulation_steps=args.gradient_accumulation_steps,
                warmup_steps=min(100, total_steps // 10),  # 动态设置warmup
                max_steps=total_steps,
                num_train_epochs=args.num_train_epochs,
                learning_rate=args.learning_rate,
                fp16=not is_bf16_supported(),
                bf16=is_bf16_supported(),
                logging_steps=args.logging_steps,
                save_steps=args.save_steps,
                optim="adamw_8bit",
                weight_decay=0.01,
                lr_scheduler_type="cosine",  # 使用cosine学习率调度
                seed=3407,
                output_dir=args.output_dir,
                report_to="none",  # 禁用wandb等
                
                # 视觉微调必需的设置
                remove_unused_columns=False,
                dataset_text_field="",
                dataset_kwargs={"skip_prepare_dataset": True},
                dataset_num_proc=1,  # 减少并行以避免问题
                max_seq_length=args.max_length,
                
                # 性能优化
                dataloader_pin_memory=False,
                dataloader_num_workers=0,  # 避免多进程问题
                save_total_limit=3,  # 限制保存的检查点数量
                load_best_model_at_end=False,  # 加速训练
            ),
        )
        
        logger.info("✅ 训练器设置完成")
        
        # 5. 显示内存使用情况
        if torch.cuda.is_available():
            gpu_stats = torch.cuda.get_device_properties(0)
            start_gpu_memory = round(torch.cuda.max_memory_reserved() / 1024 / 1024 / 1024, 3)
            max_memory = round(gpu_stats.total_memory / 1024 / 1024 / 1024, 3)
            logger.info(f"GPU: {gpu_stats.name}")
            logger.info(f"最大内存: {max_memory} GB")
            logger.info(f"已使用内存: {start_gpu_memory} GB")
        
        # 6. 开始训练
        logger.info("🚀 开始优化训练...")
        trainer_stats = trainer.train()
        
        # 7. 显示训练统计
        if torch.cuda.is_available():
            used_memory = round(torch.cuda.max_memory_reserved() / 1024 / 1024 / 1024, 3)
            used_memory_for_lora = round(used_memory - start_gpu_memory, 3)
            used_percentage = round(used_memory / max_memory * 100, 3)
            lora_percentage = round(used_memory_for_lora / max_memory * 100, 3)
            
            logger.info(f"训练时间: {trainer_stats.metrics['train_runtime']:.2f} 秒")
            logger.info(f"训练时间: {round(trainer_stats.metrics['train_runtime']/60, 2)} 分钟")
            logger.info(f"峰值内存: {used_memory} GB")
            logger.info(f"训练增加内存: {used_memory_for_lora} GB")
            logger.info(f"内存使用率: {used_percentage}%")
            logger.info(f"训练内存使用率: {lora_percentage}%")
        
        # 8. 保存模型
        logger.info("保存模型...")
        model.save_pretrained(f"{args.output_dir}/lora_model")
        tokenizer.save_pretrained(f"{args.output_dir}/lora_model")
        
        logger.info("✅ 优化训练完成！")
        logger.info(f"模型已保存到: {args.output_dir}/lora_model")
        
    except Exception as e:
        logger.error(f"训练失败: {e}")
        import traceback
        traceback.print_exc()
        return 1
    
    return 0

if __name__ == "__main__":
    sys.exit(main())
