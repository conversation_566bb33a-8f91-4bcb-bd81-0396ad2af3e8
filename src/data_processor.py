"""
数据预处理模块
处理HM3D和Matterport数据集，解析JSON文件，构建训练数据格式
"""

import json
import os
from typing import List, Dict, Any, Tuple
from PIL import Image
import torch
from torch.utils.data import Dataset
from transformers import AutoProcessor
import logging

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class NavigationDataset(Dataset):
    """导航数据集类"""

    def __init__(
        self,
        data_root: str,
        split_file: str,
        split: str = "train",
        processor=None,
        max_length: int = 512,
        image_size: Tuple[int, int] = (224, 224),
    ):
        """
        初始化数据集

        Args:
            data_root: 数据根目录
            split_file: 数据分割文件路径
            split: 数据分割类型 ("train" 或 "val")
            processor: 预训练模型的处理器
            max_length: 最大序列长度
            image_size: 图像尺寸
        """
        self.data_root = data_root
        self.split = split
        self.processor = processor
        self.max_length = max_length
        self.image_size = image_size

        # 加载数据分割信息
        with open(split_file, "r") as f:
            split_data = json.load(f)

        self.data_files = split_data[split]
        self.samples = []

        # 解析所有数据文件
        self._load_data()

        logger.info(f"Loaded {len(self.samples)} samples for {split} split")

    def _load_data(self):
        """加载并解析数据"""
        for data_file in self.data_files:
            json_path = os.path.join(self.data_root, data_file)

            if not os.path.exists(json_path):
                logger.warning(f"Data file not found: {json_path}")
                continue

            with open(json_path, "r") as f:
                scene_data = json.load(f)

            scene_dir = os.path.dirname(json_path)

            # 处理每个场景中的每个问题
            for item in scene_data.get("items", []):
                self._process_item(item, scene_dir)

    def _process_item(self, item: Dict[str, Any], scene_dir: str):
        """处理单个数据项"""
        question = item.get("question", "")
        answer = item.get("answer", "")
        path = item.get("path", [])

        if not path:
            return

        # 为每个路径步骤创建训练样本
        for i, step in enumerate(path):
            img_path = os.path.join(scene_dir, step.get("img", ""))
            action = step.get("action", "")

            if not os.path.exists(img_path):
                continue

            # 构建输入文本
            input_text = f"Question: {question}"

            # 构建目标输出
            if action in ["move_forward", "turn_left", "turn_right"]:
                target = f"<{action}>"
            elif action == "stop":
                target = answer
            else:
                continue

            sample = {
                "image_path": img_path,
                "input_text": input_text,
                "target": target,
                "action": action,
                "question": question,
                "answer": answer,
                "step_index": i,
                "total_steps": len(path),
            }

            self.samples.append(sample)

    def __len__(self):
        return len(self.samples)

    def __getitem__(self, idx):
        sample = self.samples[idx]

        # 加载图像
        try:
            image = Image.open(sample["image_path"]).convert("RGB")
            image = image.resize(self.image_size)
        except Exception as e:
            logger.error(f"Error loading image {sample['image_path']}: {e}")
            # 创建空白图像作为fallback
            image = Image.new("RGB", self.image_size, color=(0, 0, 0))

        # 如果有processor，使用它处理图像和文本
        if self.processor:
            # 构建官方格式的对话数据
            conversations = [
                {"from": "human", "value": f"<image>{sample['input_text']}"},
                {"from": "gpt", "value": sample["target"]},
            ]

            # 使用官方的预处理函数
            from qwen_data_utils import preprocess_qwen_2_visual

            # 处理图像获取grid_thw
            visual_processed = self.processor.image_processor.preprocess(
                image, return_tensors="pt"
            )
            grid_thw = visual_processed["image_grid_thw"][0]
            grid_thw_merged = grid_thw.prod() // (
                self.processor.image_processor.merge_size**2
            )

            # 预处理对话数据
            data_dict = preprocess_qwen_2_visual(
                [conversations],
                self.processor.tokenizer,
                grid_thw_image=[grid_thw_merged],
                grid_thw_video=[],
            )

            # 提取处理后的数据
            inputs = {
                "input_ids": data_dict["input_ids"],
                "attention_mask": torch.ones_like(data_dict["input_ids"]),
                "pixel_values": visual_processed["pixel_values"],
                "image_grid_thw": grid_thw.unsqueeze(0),
            }

            result = {
                "input_ids": inputs["input_ids"].squeeze(0),
                "attention_mask": inputs["attention_mask"].squeeze(0),
                "pixel_values": inputs["pixel_values"].squeeze(0),
                "labels": data_dict["labels"].squeeze(0),
                "target_text": sample["target"],
                "action": sample["action"],
                "question": sample["question"],
                "answer": sample["answer"],
            }

            # 如果processor输出包含image_grid_thw，则添加它
            if "image_grid_thw" in inputs:
                result["image_grid_thw"] = inputs["image_grid_thw"].squeeze(0)

            return result
        else:
            return {
                "image": image,
                "input_text": sample["input_text"],
                "target": sample["target"],
                "action": sample["action"],
                "question": sample["question"],
                "answer": sample["answer"],
            }


def create_data_loaders(
    data_root: str,
    split_file: str,
    processor,
    batch_size: int = 8,
    num_workers: int = 4,
    max_length: int = 512,
):
    """
    创建训练和验证数据加载器

    Args:
        data_root: 数据根目录
        split_file: 数据分割文件路径
        processor: 预训练模型的处理器
        batch_size: 批次大小
        num_workers: 数据加载工作进程数
        max_length: 最大序列长度

    Returns:
        train_loader, val_loader: 训练和验证数据加载器
    """
    from torch.utils.data import DataLoader

    # 创建数据集
    train_dataset = NavigationDataset(
        data_root=data_root,
        split_file=split_file,
        split="train",
        processor=processor,
        max_length=max_length,
    )

    val_dataset = NavigationDataset(
        data_root=data_root,
        split_file=split_file,
        split="val",
        processor=processor,
        max_length=max_length,
    )

    # 创建数据加载器
    train_loader = DataLoader(
        train_dataset,
        batch_size=batch_size,
        shuffle=True,
        num_workers=num_workers,
        pin_memory=True,
        drop_last=True,
    )

    val_loader = DataLoader(
        val_dataset,
        batch_size=batch_size,
        shuffle=False,
        num_workers=num_workers,
        pin_memory=True,
        drop_last=False,
    )

    return train_loader, val_loader


def collate_fn(batch):
    """自定义批次整理函数"""
    # 提取各个字段
    input_ids = [item["input_ids"] for item in batch]
    attention_mask = [item["attention_mask"] for item in batch]
    pixel_values = [item["pixel_values"] for item in batch]
    labels = [item["labels"] for item in batch]

    # 填充到相同长度
    input_ids = torch.nn.utils.rnn.pad_sequence(
        input_ids, batch_first=True, padding_value=0
    )
    attention_mask = torch.nn.utils.rnn.pad_sequence(
        attention_mask, batch_first=True, padding_value=0
    )
    labels = torch.nn.utils.rnn.pad_sequence(
        labels, batch_first=True, padding_value=-100
    )

    # 图像数据处理 - 对于Qwen2.5VL需要特殊处理
    if len(pixel_values) > 0 and pixel_values[0] is not None:
        # 检查是否有image_grid_thw信息
        if hasattr(pixel_values[0], "shape") and len(pixel_values[0].shape) == 4:
            # 标准格式: [batch, channels, height, width]
            pixel_values = torch.stack(pixel_values)
        else:
            # 可能是嵌套的tensor，需要特殊处理
            try:
                pixel_values = torch.stack(pixel_values)
            except:
                # 如果无法堆叠，使用第一个作为模板
                pixel_values = pixel_values[0].unsqueeze(0).repeat(len(batch), 1, 1, 1)
    else:
        # 创建虚拟的pixel_values
        pixel_values = torch.zeros((len(batch), 3, 224, 224))

    # 处理image_grid_thw
    batch_size = len(batch)

    # 尝试从batch中获取image_grid_thw
    if "image_grid_thw" in batch[0]:
        image_grid_thw = [item["image_grid_thw"] for item in batch]
        image_grid_thw = torch.stack(image_grid_thw)
    else:
        # 如果没有，则使用默认值
        image_grid_thw = torch.tensor([[1, 224, 224]] * batch_size, dtype=torch.long)

    return {
        "input_ids": input_ids,
        "attention_mask": attention_mask,
        "pixel_values": pixel_values,
        "image_grid_thw": image_grid_thw,
        "labels": labels,
        "target_texts": [item["target_text"] for item in batch],
        "actions": [item["action"] for item in batch],
        "questions": [item["question"] for item in batch],
        "answers": [item["answer"] for item in batch],
    }


if __name__ == "__main__":
    # 测试数据加载
    data_root = "data/finetune"
    split_file = "data/finetune/split.json"

    # 创建简单的测试数据集
    dataset = NavigationDataset(
        data_root=data_root, split_file=split_file, split="train", processor=None
    )

    print(f"Dataset size: {len(dataset)}")
    if len(dataset) > 0:
        sample = dataset[0]
        print("Sample keys:", sample.keys())
        print("Input text:", sample["input_text"])
        print("Target:", sample["target"])
        print("Action:", sample["action"])
