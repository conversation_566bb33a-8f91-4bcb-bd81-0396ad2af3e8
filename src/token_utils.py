"""
特殊Token处理模块
为模型添加导航动作的特殊token：<move_forward>、<turn_left>、<turn_right>
"""

import torch
from transformers import AutoTokenizer, AutoProcessor
import logging

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class NavigationTokenizer:
    """导航任务的特殊tokenizer"""

    # 定义特殊的导航动作token
    NAVIGATION_TOKENS = ["<move_forward>", "<turn_left>", "<turn_right>"]

    def __init__(self, base_tokenizer_name: str = "Qwen/Qwen2.5-VL-3B-Instruct"):
        """
        初始化导航tokenizer

        Args:
            base_tokenizer_name: 基础tokenizer名称
        """
        self.base_tokenizer_name = base_tokenizer_name
        self.tokenizer = None
        self.processor = None
        self.original_vocab_size = None
        self.new_vocab_size = None

        self._setup_tokenizer()

    def _setup_tokenizer(self):
        """设置tokenizer并添加特殊token"""
        try:
            # 加载基础tokenizer
            self.tokenizer = AutoTokenizer.from_pretrained(
                self.base_tokenizer_name, trust_remote_code=True
            )

            # 记录原始词汇表大小
            self.original_vocab_size = len(self.tokenizer)

            # 添加特殊token
            special_tokens_dict = {"additional_special_tokens": self.NAVIGATION_TOKENS}

            num_added_tokens = self.tokenizer.add_special_tokens(special_tokens_dict)
            self.new_vocab_size = len(self.tokenizer)

            logger.info(f"Added {num_added_tokens} special tokens")
            logger.info(
                f"Vocabulary size: {self.original_vocab_size} -> {self.new_vocab_size}"
            )

            # 验证token是否正确添加
            for token in self.NAVIGATION_TOKENS:
                token_id = self.tokenizer.convert_tokens_to_ids(token)
                logger.info(f"Token '{token}' -> ID: {token_id}")

        except Exception as e:
            logger.error(f"Error setting up tokenizer: {e}")
            raise

    def setup_processor(self):
        """设置processor（用于视觉-语言模型）"""
        try:
            self.processor = AutoProcessor.from_pretrained(
                self.base_tokenizer_name, trust_remote_code=True
            )

            # 更新processor的tokenizer
            self.processor.tokenizer = self.tokenizer

            logger.info("Processor setup completed")
            return self.processor

        except Exception as e:
            logger.error(f"Error setting up processor: {e}")
            raise

    def get_navigation_token_ids(self):
        """获取导航token的ID映射"""
        token_ids = {}
        for token in self.NAVIGATION_TOKENS:
            token_ids[token] = self.tokenizer.convert_tokens_to_ids(token)
        return token_ids

    def encode_navigation_action(self, action: str):
        """
        编码导航动作为token ID

        Args:
            action: 动作字符串 ('move_forward', 'turn_left', 'turn_right')

        Returns:
            token_id: 对应的token ID
        """
        token_map = {
            "move_forward": "<move_forward>",
            "turn_left": "<turn_left>",
            "turn_right": "<turn_right>",
        }

        if action in token_map:
            token = token_map[action]
            return self.tokenizer.convert_tokens_to_ids(token)
        else:
            logger.warning(f"Unknown action: {action}")
            return None

    def decode_navigation_action(self, token_id: int):
        """
        解码token ID为导航动作

        Args:
            token_id: token ID

        Returns:
            action: 动作字符串
        """
        token = self.tokenizer.convert_ids_to_tokens(token_id)

        action_map = {
            "<move_forward>": "move_forward",
            "<turn_left>": "turn_left",
            "<turn_right>": "turn_right",
        }

        return action_map.get(token, None)

    def is_navigation_token(self, token_id: int):
        """检查token ID是否为导航token"""
        token = self.tokenizer.convert_ids_to_tokens(token_id)
        return token in self.NAVIGATION_TOKENS

    def get_tokenizer(self):
        """获取tokenizer"""
        return self.tokenizer

    def get_processor(self):
        """获取processor"""
        if self.processor is None:
            self.setup_processor()
        return self.processor

    def resize_model_embeddings(self, model):
        """
        调整模型的embedding层大小以适应新的词汇表

        Args:
            model: 要调整的模型

        Returns:
            model: 调整后的模型
        """
        if self.new_vocab_size > self.original_vocab_size:
            logger.info(
                f"Resizing model embeddings from {self.original_vocab_size} to {self.new_vocab_size}"
            )
            model.resize_token_embeddings(self.new_vocab_size)

            # 更新模型配置中的vocab_size
            model.config.vocab_size = self.new_vocab_size

            # 初始化新token的embedding
            with torch.no_grad():
                # 获取新添加的token的embedding
                new_embeddings = model.get_input_embeddings().weight[
                    -len(self.NAVIGATION_TOKENS) :
                ]

                # 使用现有token的平均值初始化新token
                existing_embeddings = model.get_input_embeddings().weight[
                    : -len(self.NAVIGATION_TOKENS)
                ]
                mean_embedding = existing_embeddings.mean(dim=0)

                for i in range(len(self.NAVIGATION_TOKENS)):
                    new_embeddings[i] = (
                        mean_embedding + torch.randn_like(mean_embedding) * 0.02
                    )

            logger.info("Model embeddings resized and initialized")

        return model


def test_navigation_tokenizer():
    """测试导航tokenizer"""
    print("Testing NavigationTokenizer...")

    try:
        # 创建tokenizer
        nav_tokenizer = NavigationTokenizer()

        # 测试编码和解码
        actions = ["move_forward", "turn_left", "turn_right"]

        for action in actions:
            token_id = nav_tokenizer.encode_navigation_action(action)
            decoded_action = nav_tokenizer.decode_navigation_action(token_id)
            print(
                f"Action: {action} -> Token ID: {token_id} -> Decoded: {decoded_action}"
            )

        # 测试tokenizer基本功能
        text = "Navigate to the kitchen. <move_forward> <turn_left> <move_forward>"
        tokens = nav_tokenizer.tokenizer.encode(text)
        decoded = nav_tokenizer.tokenizer.decode(tokens)
        print(f"\nOriginal: {text}")
        print(f"Decoded: {decoded}")

        # 获取导航token ID映射
        token_ids = nav_tokenizer.get_navigation_token_ids()
        print(f"\nNavigation token IDs: {token_ids}")

        print("NavigationTokenizer test completed successfully!")

    except Exception as e:
        print(f"Error in test: {e}")


if __name__ == "__main__":
    test_navigation_tokenizer()
