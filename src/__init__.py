"""
导航模型微调项目核心模块

包含数据处理、模型配置、训练和推理等核心功能
"""

from .data_processor import NavigationDataset, create_data_loaders, collate_fn
from .model_config import ModelConfig, NavigationModel, create_training_arguments
from .trainer import NavigationTrainer, train_navigation_model, setup_swanlab, setup_wandb
from .inference import NavigationInference
from .token_utils import NavigationTokenizer
from .qwen_data_utils import preprocess_qwen_2_visual

__version__ = "1.0.0"
__author__ = "EQACL Team"

__all__ = [
    "NavigationDataset",
    "create_data_loaders", 
    "collate_fn",
    "ModelConfig",
    "NavigationModel",
    "create_training_arguments",
    "NavigationTrainer",
    "train_navigation_model",
    "setup_swanlab",
    "setup_wandb",
    "NavigationInference",
    "NavigationTokenizer",
    "preprocess_qwen_2_visual",
]
