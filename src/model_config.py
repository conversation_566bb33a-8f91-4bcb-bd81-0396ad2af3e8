"""
模型配置和加载模块
配置Qwen2.5VL-3B模型，设置LoRA微调参数
"""

import torch
from transformers import (
    AutoModelForCausalLM,
    AutoProcessor,
    BitsAndBytesConfig,
    TrainingArguments,
)

try:
    from transformers import Qwen2VLForConditionalGeneration
except ImportError:
    try:
        from transformers import AutoModel

        Qwen2VLForConditionalGeneration = AutoModel
    except ImportError:
        Qwen2VLForConditionalGeneration = None
from peft import LoraConfig, get_peft_model, TaskType, prepare_model_for_kbit_training
import logging
from typing import Optional, Dict, Any

try:
    from .token_utils import NavigationTokenizer
except ImportError:
    from token_utils import NavigationTokenizer

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class ModelConfig:
    """模型配置类"""

    def __init__(
        self,
        model_name: str = "data/Qwen2.5-VL-3B-Instruct",
        use_4bit: bool = True,
        use_lora: bool = True,
        lora_rank: int = 16,
        lora_alpha: int = 32,
        lora_dropout: float = 0.1,
        target_modules: Optional[list] = None,
        device_map: str = "auto",
        torch_dtype: torch.dtype = torch.bfloat16,
    ):
        """
        初始化模型配置

        Args:
            model_name: 模型名称或路径
            use_4bit: 是否使用4bit量化
            use_lora: 是否使用LoRA微调
            lora_rank: LoRA rank
            lora_alpha: LoRA alpha
            lora_dropout: LoRA dropout
            target_modules: LoRA目标模块
            device_map: 设备映射
            torch_dtype: 数据类型
        """
        self.model_name = model_name
        self.use_4bit = use_4bit
        self.use_lora = use_lora
        self.lora_rank = lora_rank
        self.lora_alpha = lora_alpha
        self.lora_dropout = lora_dropout
        self.device_map = device_map
        self.torch_dtype = torch_dtype

        # 默认LoRA目标模块（针对Qwen2.5VL）
        if target_modules is None:
            self.target_modules = [
                "q_proj",
                "k_proj",
                "v_proj",
                "o_proj",
                "gate_proj",
                "up_proj",
                "down_proj",
            ]
        else:
            self.target_modules = target_modules

    def get_quantization_config(self):
        """获取量化配置"""
        if not self.use_4bit:
            return None

        return BitsAndBytesConfig(
            load_in_4bit=True,
            bnb_4bit_use_double_quant=True,
            bnb_4bit_quant_type="nf4",
            bnb_4bit_compute_dtype=self.torch_dtype,
        )

    def get_lora_config(self):
        """获取LoRA配置"""
        if not self.use_lora:
            return None

        return LoraConfig(
            task_type=TaskType.CAUSAL_LM,
            r=self.lora_rank,
            lora_alpha=self.lora_alpha,
            lora_dropout=self.lora_dropout,
            target_modules=self.target_modules,
            bias="none",
        )


class NavigationModel:
    """导航模型类"""

    def __init__(self, config: ModelConfig):
        """
        初始化导航模型

        Args:
            config: 模型配置
        """
        self.config = config
        self.model = None
        self.processor = None
        self.tokenizer = None
        self.nav_tokenizer = None

        self._setup_model()

    def _setup_model(self):
        """设置模型"""
        logger.info(f"Loading model: {self.config.model_name}")

        try:
            # 设置导航tokenizer
            self.nav_tokenizer = NavigationTokenizer(self.config.model_name)
            self.tokenizer = self.nav_tokenizer.get_tokenizer()
            self.processor = self.nav_tokenizer.get_processor()

            # 获取量化配置
            quantization_config = self.config.get_quantization_config()

            # 加载模型 - 使用官方推荐的方式
            try:
                # 根据模型名称选择正确的模型类
                if "qwen2.5" in self.config.model_name.lower():
                    from transformers import Qwen2_5_VLForConditionalGeneration

                    self.model = Qwen2_5_VLForConditionalGeneration.from_pretrained(
                        self.config.model_name,
                        quantization_config=quantization_config,
                        device_map=self.config.device_map,
                        torch_dtype=self.config.torch_dtype,
                        trust_remote_code=True,
                        attn_implementation="eager",  # 避免flash attention问题
                    )
                else:
                    from transformers import Qwen2VLForConditionalGeneration

                    self.model = Qwen2VLForConditionalGeneration.from_pretrained(
                        self.config.model_name,
                        quantization_config=quantization_config,
                        device_map=self.config.device_map,
                        torch_dtype=self.config.torch_dtype,
                        trust_remote_code=True,
                        attn_implementation="eager",
                    )

                # 禁用缓存以支持训练
                self.model.config.use_cache = False

            except Exception as e:
                logger.error(f"Failed to load model: {e}")
                raise

            # 调整embedding大小以适应新token
            self.model = self.nav_tokenizer.resize_model_embeddings(self.model)

            # 如果使用量化，准备模型进行训练
            if self.config.use_4bit:
                self.model = prepare_model_for_kbit_training(self.model)

            # 应用LoRA
            if self.config.use_lora:
                lora_config = self.config.get_lora_config()
                self.model = get_peft_model(self.model, lora_config)

                # 打印可训练参数
                self.print_trainable_parameters()

            logger.info("Model setup completed successfully")

        except Exception as e:
            logger.error(f"Error setting up model: {e}")
            raise

    def _prepare_inputs_for_generation(self, input_ids, **kwargs):
        """为生成准备输入的辅助方法"""
        # 基本的输入准备逻辑
        model_inputs = {"input_ids": input_ids}

        # 添加其他必要的输入
        for key in ["attention_mask", "pixel_values", "image_grid_thw"]:
            if key in kwargs:
                model_inputs[key] = kwargs[key]

        return model_inputs

    def print_trainable_parameters(self):
        """打印可训练参数信息"""
        if hasattr(self.model, "print_trainable_parameters"):
            self.model.print_trainable_parameters()
        else:
            trainable_params = 0
            all_param = 0
            for _, param in self.model.named_parameters():
                all_param += param.numel()
                if param.requires_grad:
                    trainable_params += param.numel()

            logger.info(
                f"Trainable params: {trainable_params:,} || "
                f"All params: {all_param:,} || "
                f"Trainable%: {100 * trainable_params / all_param:.2f}%"
            )

    def get_model(self):
        """获取模型"""
        return self.model

    def get_processor(self):
        """获取processor"""
        return self.processor

    def get_tokenizer(self):
        """获取tokenizer"""
        return self.tokenizer

    def get_navigation_tokenizer(self):
        """获取导航tokenizer"""
        return self.nav_tokenizer

    def save_model(self, save_path: str):
        """保存模型"""
        logger.info(f"Saving model to: {save_path}")

        if self.config.use_lora:
            # 保存LoRA权重
            self.model.save_pretrained(save_path)
        else:
            # 保存完整模型
            self.model.save_pretrained(save_path)

        # 保存tokenizer
        self.tokenizer.save_pretrained(save_path)

        logger.info("Model saved successfully")

    def load_model(self, load_path: str):
        """加载模型"""
        logger.info(f"Loading model from: {load_path}")

        try:
            if self.config.use_lora:
                # 加载LoRA权重
                from peft import PeftModel

                self.model = PeftModel.from_pretrained(self.model, load_path)
            else:
                # 加载完整模型
                self.model = AutoModelForCausalLM.from_pretrained(load_path)

            logger.info("Model loaded successfully")

        except Exception as e:
            logger.error(f"Error loading model: {e}")
            raise


def create_training_arguments(
    output_dir: str = "./results",
    num_train_epochs: int = 3,
    per_device_train_batch_size: int = 4,
    per_device_eval_batch_size: int = 4,
    gradient_accumulation_steps: int = 4,
    learning_rate: float = 2e-4,
    weight_decay: float = 0.01,
    warmup_ratio: float = 0.1,
    logging_steps: int = 10,
    save_steps: int = 500,
    eval_steps: int = 500,
    save_total_limit: int = 3,
    fp16: bool = False,
    bf16: bool = True,
    gradient_checkpointing: bool = True,
    dataloader_pin_memory: bool = False,
    **kwargs,
) -> TrainingArguments:
    """
    创建训练参数

    Args:
        output_dir: 输出目录
        num_train_epochs: 训练轮数
        per_device_train_batch_size: 每设备训练批次大小
        per_device_eval_batch_size: 每设备验证批次大小
        gradient_accumulation_steps: 梯度累积步数
        learning_rate: 学习率
        weight_decay: 权重衰减
        warmup_ratio: 预热比例
        logging_steps: 日志步数
        save_steps: 保存步数
        eval_steps: 评估步数
        save_total_limit: 保存模型数量限制
        fp16: 是否使用fp16
        bf16: 是否使用bf16
        gradient_checkpointing: 是否使用梯度检查点
        dataloader_pin_memory: 是否固定内存
        **kwargs: 其他参数

    Returns:
        TrainingArguments: 训练参数对象
    """
    return TrainingArguments(
        output_dir=output_dir,
        num_train_epochs=num_train_epochs,
        per_device_train_batch_size=per_device_train_batch_size,
        per_device_eval_batch_size=per_device_eval_batch_size,
        gradient_accumulation_steps=gradient_accumulation_steps,
        learning_rate=learning_rate,
        weight_decay=weight_decay,
        warmup_ratio=warmup_ratio,
        logging_steps=logging_steps,
        save_steps=save_steps,
        eval_steps=eval_steps,
        save_total_limit=save_total_limit,
        eval_strategy="steps",
        save_strategy="steps",
        load_best_model_at_end=True,
        metric_for_best_model="eval_loss",
        greater_is_better=False,
        fp16=fp16,
        bf16=bf16,
        gradient_checkpointing=gradient_checkpointing,
        dataloader_pin_memory=dataloader_pin_memory,
        remove_unused_columns=False,
        **kwargs,
    )


if __name__ == "__main__":
    # 测试模型配置和加载
    print("Testing model configuration...")

    try:
        # 创建配置
        config = ModelConfig(
            model_name="data/Qwen2.5-VL-3B-Instruct", use_4bit=True, use_lora=True
        )

        print("Model config created successfully")
        print(f"LoRA config: {config.get_lora_config()}")
        print(f"Quantization config: {config.get_quantization_config()}")

        # 注意：实际加载模型需要模型文件存在
        # nav_model = NavigationModel(config)
        # print("Model loaded successfully")

    except Exception as e:
        print(f"Error in test: {e}")
