"""
训练循环实现
包括损失计算、梯度更新、验证评估等核心训练逻辑
"""

import torch
import torch.nn.functional as F
from torch.utils.data import DataLoader
from transformers import Trainer, TrainingArguments, TrainerCallback
from transformers.trainer_utils import EvalPrediction
import numpy as np
from typing import Dict, List, Optional, Union, Any
import logging
from tqdm import tqdm

try:
    import swanlab

    SWANLAB_AVAILABLE = True
except ImportError:
    SWANLAB_AVAILABLE = False
    swanlab = None
import os

try:
    from .model_config import NavigationModel, ModelConfig
    from .data_processor import NavigationDataset, collate_fn
except ImportError:
    from model_config import NavigationModel, ModelConfig
    from data_processor import NavigationDataset, collate_fn

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class SwanLabCallback(TrainerCallback):
    """SwanLab日志回调"""

    def __init__(self):
        self.swanlab_available = SWANLAB_AVAILABLE

    def on_train_begin(self, args, state, control, **kwargs):
        if not self.swanlab_available:
            return

    def on_log(self, args, state, control, logs=None, **kwargs):
        if not self.swanlab_available or logs is None:
            return

        # 记录训练指标
        try:
            step = state.global_step
            for key, value in logs.items():
                if isinstance(value, (int, float)):
                    swanlab.log({key: value}, step=step)
        except RuntimeError:
            # SwanLab未初始化，跳过日志记录
            pass

    def on_evaluate(self, args, state, control, logs=None, **kwargs):
        if not self.swanlab_available or logs is None:
            return

        # 记录评估指标
        step = state.global_step
        for key, value in logs.items():
            if isinstance(value, (int, float)):
                swanlab.log({f"eval_{key}": value}, step=step)


class NavigationTrainer(Trainer):
    """自定义导航训练器"""

    def __init__(
        self,
        model,
        args: TrainingArguments,
        train_dataset: NavigationDataset,
        eval_dataset: NavigationDataset,
        tokenizer,
        nav_tokenizer,
        **kwargs,
    ):
        """
        初始化训练器

        Args:
            model: 模型
            args: 训练参数
            train_dataset: 训练数据集
            eval_dataset: 验证数据集
            tokenizer: tokenizer
            nav_tokenizer: 导航tokenizer
            **kwargs: 其他参数
        """
        self.nav_tokenizer = nav_tokenizer
        self.navigation_token_ids = nav_tokenizer.get_navigation_token_ids()

        # 添加SwanLab回调
        callbacks = kwargs.get("callbacks", [])
        if SWANLAB_AVAILABLE:
            callbacks.append(SwanLabCallback())
        kwargs["callbacks"] = callbacks

        super().__init__(
            model=model,
            args=args,
            train_dataset=train_dataset,
            eval_dataset=eval_dataset,
            tokenizer=tokenizer,
            data_collator=collate_fn,
            **kwargs,
        )

    def compute_loss(
        self, model, inputs, return_outputs=False, num_items_in_batch=None
    ):
        """
        计算损失

        Args:
            model: 模型
            inputs: 输入数据
            return_outputs: 是否返回输出
            num_items_in_batch: 批次中的项目数（新版transformers参数）

        Returns:
            loss: 损失值
            outputs: 模型输出（如果return_outputs=True）
        """
        labels = inputs.get("labels")

        # 前向传播 - 使用标准方式
        outputs = model(
            input_ids=inputs.get("input_ids"),
            attention_mask=inputs.get("attention_mask"),
            pixel_values=inputs.get("pixel_values"),
            image_grid_thw=inputs.get("image_grid_thw"),
            labels=labels,
        )

        # 计算损失
        if labels is not None:
            # 使用模型内置的损失计算
            loss = outputs.loss

            # 可以添加自定义损失计算逻辑
            # 例如：对导航token给予更高权重
            if hasattr(self, "apply_navigation_loss_weighting"):
                loss = self.apply_navigation_loss_weighting(outputs, labels, loss)
        else:
            loss = None

        return (loss, outputs) if return_outputs else loss

    def apply_navigation_loss_weighting(self, outputs, labels, base_loss):
        """
        对导航token应用权重

        Args:
            outputs: 模型输出
            labels: 标签
            base_loss: 基础损失

        Returns:
            weighted_loss: 加权后的损失
        """
        logits = outputs.logits
        shift_logits = logits[..., :-1, :].contiguous()
        shift_labels = labels[..., 1:].contiguous()

        # 创建权重掩码
        weight_mask = torch.ones_like(shift_labels, dtype=torch.float)

        # 对导航token给予更高权重
        for token_id in self.navigation_token_ids.values():
            nav_mask = shift_labels == token_id
            weight_mask[nav_mask] = 2.0  # 导航token权重为2倍

        # 计算加权损失
        loss_fct = torch.nn.CrossEntropyLoss(reduction="none")
        flat_logits = shift_logits.view(-1, shift_logits.size(-1))
        flat_labels = shift_labels.view(-1)
        flat_weights = weight_mask.view(-1)

        losses = loss_fct(flat_logits, flat_labels)
        weighted_losses = losses * flat_weights

        # 只计算非-100标签的损失
        valid_mask = flat_labels != -100
        if valid_mask.sum() > 0:
            weighted_loss = weighted_losses[valid_mask].mean()
        else:
            weighted_loss = base_loss

        return weighted_loss

    def evaluate(
        self,
        eval_dataset: Optional[NavigationDataset] = None,
        ignore_keys: Optional[List[str]] = None,
        metric_key_prefix: str = "eval",
    ) -> Dict[str, float]:
        """
        评估模型

        Args:
            eval_dataset: 评估数据集
            ignore_keys: 忽略的键
            metric_key_prefix: 指标前缀

        Returns:
            metrics: 评估指标
        """
        # 调用父类评估方法
        metrics = super().evaluate(eval_dataset, ignore_keys, metric_key_prefix)

        # 添加自定义评估指标
        if eval_dataset is not None:
            custom_metrics = self.compute_custom_metrics(eval_dataset)
            metrics.update(
                {f"{metric_key_prefix}_{k}": v for k, v in custom_metrics.items()}
            )

        return metrics

    def compute_custom_metrics(
        self, eval_dataset: NavigationDataset
    ) -> Dict[str, float]:
        """
        计算自定义评估指标

        Args:
            eval_dataset: 评估数据集

        Returns:
            metrics: 自定义指标
        """
        self.model.eval()

        total_samples = 0
        correct_actions = 0
        navigation_accuracy = 0
        navigation_samples = 0

        eval_dataloader = DataLoader(
            eval_dataset,
            batch_size=self.args.per_device_eval_batch_size,
            collate_fn=collate_fn,
            shuffle=False,
        )

        with torch.no_grad():
            for batch in tqdm(eval_dataloader, desc="Computing custom metrics"):
                batch = {
                    k: v.to(self.model.device) if isinstance(v, torch.Tensor) else v
                    for k, v in batch.items()
                }

                outputs = self.model.generate(
                    input_ids=batch["input_ids"],
                    attention_mask=batch["attention_mask"],
                    pixel_values=batch["pixel_values"],
                    image_grid_thw=batch["image_grid_thw"],
                    max_new_tokens=10,
                    do_sample=False,
                    pad_token_id=self.tokenizer.eos_token_id,
                )

                # 解码预测结果
                predictions = self.tokenizer.batch_decode(
                    outputs[:, batch["input_ids"].shape[1] :], skip_special_tokens=False
                )

                # 计算准确率
                for i, (pred, target_text, action) in enumerate(
                    zip(predictions, batch["target_texts"], batch["actions"])
                ):
                    total_samples += 1

                    # 检查是否为导航动作
                    if action in ["move_forward", "turn_left", "turn_right"]:
                        navigation_samples += 1
                        expected_token = f"<{action}>"
                        if expected_token in pred:
                            navigation_accuracy += 1
                            correct_actions += 1
                    else:
                        # 对于文本答案，使用简单的包含检查
                        if target_text.lower() in pred.lower():
                            correct_actions += 1

        # 计算指标
        overall_accuracy = correct_actions / total_samples if total_samples > 0 else 0.0
        nav_accuracy = (
            navigation_accuracy / navigation_samples if navigation_samples > 0 else 0.0
        )

        return {
            "overall_accuracy": overall_accuracy,
            "navigation_accuracy": nav_accuracy,
            "navigation_samples_ratio": (
                navigation_samples / total_samples if total_samples > 0 else 0.0
            ),
        }


def train_navigation_model(
    model_config: ModelConfig,
    training_args: TrainingArguments,
    train_dataset: NavigationDataset,
    eval_dataset: NavigationDataset,
    resume_from_checkpoint: Optional[str] = None,
):
    """
    训练导航模型

    Args:
        model_config: 模型配置
        training_args: 训练参数
        train_dataset: 训练数据集
        eval_dataset: 验证数据集
        resume_from_checkpoint: 从检查点恢复

    Returns:
        trainer: 训练器
        model: 训练后的模型
    """
    logger.info("Starting navigation model training...")

    # 初始化模型
    nav_model = NavigationModel(model_config)
    model = nav_model.get_model()
    tokenizer = nav_model.get_tokenizer()
    nav_tokenizer = nav_model.get_navigation_tokenizer()

    # 创建训练器
    trainer = NavigationTrainer(
        model=model,
        args=training_args,
        train_dataset=train_dataset,
        eval_dataset=eval_dataset,
        tokenizer=tokenizer,
        nav_tokenizer=nav_tokenizer,
    )

    # 开始训练
    if resume_from_checkpoint:
        logger.info(f"Resuming training from checkpoint: {resume_from_checkpoint}")
        trainer.train(resume_from_checkpoint=resume_from_checkpoint)
    else:
        trainer.train()

    # 保存最终模型
    final_model_path = os.path.join(training_args.output_dir, "final_model")
    nav_model.save_model(final_model_path)

    logger.info("Training completed successfully!")

    return trainer, nav_model


def setup_swanlab(
    project_name: str = "navigation-finetune", run_name: Optional[str] = None
):
    """
    设置SwanLab日志

    Args:
        project_name: 项目名称
        run_name: 运行名称
    """
    if not SWANLAB_AVAILABLE:
        logger.warning("SwanLab not available, skipping initialization")
        return

    try:
        swanlab.init(
            project=project_name,
            experiment_name=run_name,
            config={
                "model": "Qwen2.5-VL-3B",
                "task": "navigation",
                "framework": "transformers",
            },
        )
        logger.info("SwanLab initialized successfully")
    except Exception as e:
        logger.warning(f"Failed to initialize SwanLab: {e}")


# 保持向后兼容
def setup_wandb(
    project_name: str = "navigation-finetune", run_name: Optional[str] = None
):
    """向后兼容的函数，实际调用setup_swanlab"""
    setup_swanlab(project_name, run_name)


if __name__ == "__main__":
    # 测试训练器
    print("Testing NavigationTrainer...")

    try:
        from .model_config import ModelConfig, create_training_arguments
        from .data_processor import NavigationDataset

        # 创建配置
        model_config = ModelConfig(use_4bit=False, use_lora=True)  # 测试时不使用量化
        training_args = create_training_arguments(
            output_dir="./test_results",
            num_train_epochs=1,
            per_device_train_batch_size=1,
            logging_steps=1,
        )

        print("Configurations created successfully")

        # 注意：实际训练需要真实的数据集和模型
        # train_dataset = NavigationDataset(...)
        # eval_dataset = NavigationDataset(...)
        # trainer, model = train_navigation_model(model_config, training_args, train_dataset, eval_dataset)

    except Exception as e:
        print(f"Error in test: {e}")
