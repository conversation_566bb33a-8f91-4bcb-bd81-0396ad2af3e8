{"mode": "train", "data_root": "data/finetune", "split_file": "data/finetune/split.json", "model_name": "data/Qwen2.5-VL-3B-Instruct", "model_path": null, "output_dir": "./test_training_swanlab", "num_train_epochs": 1, "per_device_train_batch_size": 1, "per_device_eval_batch_size": 2, "gradient_accumulation_steps": 4, "learning_rate": 0.0002, "weight_decay": 0.01, "warmup_ratio": 0.1, "max_length": 256, "use_lora": true, "lora_rank": 16, "lora_alpha": 32, "lora_dropout": 0.1, "use_4bit": true, "resume_from_checkpoint": null, "use_swanlab": true, "swanlab_project": "navigation-finetune-test", "use_wandb": false, "wandb_project": "navigation-finetune", "run_name": "test-run-20250616-171828", "image_path": null, "question": null, "eval_dataset": null, "eval_output": null}