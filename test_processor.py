#!/usr/bin/env python3
"""
测试processor输出，检查image_grid_thw参数
"""

import torch
from PIL import Image
from src.token_utils import NavigationTokenizer

def test_processor_output():
    """测试processor的输出"""
    print("=" * 50)
    print("测试Processor输出")
    print("=" * 50)
    
    try:
        # 初始化tokenizer和processor
        nav_tokenizer = NavigationTokenizer("data/Qwen2.5-VL-3B-Instruct")
        processor = nav_tokenizer.setup_processor()
        
        # 创建测试图像
        test_image = Image.new('RGB', (224, 224), color=(128, 128, 128))
        test_text = "Question: Where should I go?"
        
        print("处理输入...")
        
        # 处理输入
        inputs = processor(
            text=test_text,
            images=test_image,
            return_tensors="pt"
        )
        
        print("Processor输出键:", list(inputs.keys()))
        
        for key, value in inputs.items():
            if isinstance(value, torch.Tensor):
                print(f"{key}: shape={value.shape}, dtype={value.dtype}")
                if key == "image_grid_thw":
                    print(f"  值: {value}")
            else:
                print(f"{key}: {type(value)}")
        
        # 检查是否有image_grid_thw
        if "image_grid_thw" in inputs:
            print("\n✓ Processor输出包含image_grid_thw")
            print(f"image_grid_thw值: {inputs['image_grid_thw']}")
        else:
            print("\n✗ Processor输出不包含image_grid_thw")
            print("需要手动添加image_grid_thw")
            
            # 手动添加
            inputs["image_grid_thw"] = torch.tensor([[1, 224, 224]], dtype=torch.long)
            print(f"手动添加的image_grid_thw: {inputs['image_grid_thw']}")
        
        return True
        
    except Exception as e:
        print(f"✗ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_data_processor():
    """测试数据处理器"""
    print("\n" + "=" * 50)
    print("测试数据处理器")
    print("=" * 50)
    
    try:
        from src.data_processor import NavigationDataset
        from src.token_utils import NavigationTokenizer
        
        # 检查数据文件
        data_root = "data/finetune"
        split_file = "data/finetune/split.json"
        
        import os
        if not os.path.exists(split_file):
            print(f"警告：分割文件不存在: {split_file}")
            return False
        
        # 初始化processor
        nav_tokenizer = NavigationTokenizer("data/Qwen2.5-VL-3B-Instruct")
        processor = nav_tokenizer.setup_processor()
        
        # 创建数据集
        dataset = NavigationDataset(
            data_root=data_root,
            split_file=split_file,
            split="train",
            processor=processor,
            max_length=512
        )
        
        print(f"数据集大小: {len(dataset)}")
        
        if len(dataset) > 0:
            sample = dataset[0]
            print("样本键:", list(sample.keys()))
            
            # 检查是否有image_grid_thw
            if "image_grid_thw" in sample:
                print(f"✓ 样本包含image_grid_thw: {sample['image_grid_thw']}")
            else:
                print("✗ 样本不包含image_grid_thw")
            
            # 检查tensor形状
            for key, value in sample.items():
                if isinstance(value, torch.Tensor):
                    print(f"{key}: shape={value.shape}")
        
        return True
        
    except Exception as e:
        print(f"✗ 数据处理器测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("开始测试...")
    
    # 测试processor
    success1 = test_processor_output()
    
    # 测试数据处理器
    success2 = test_data_processor()
    
    if success1 and success2:
        print("\n" + "=" * 50)
        print("✓ 所有测试通过")
        print("=" * 50)
    else:
        print("\n" + "=" * 50)
        print("✗ 部分测试失败")
        print("=" * 50)
