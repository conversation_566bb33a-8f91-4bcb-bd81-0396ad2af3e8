"""
主训练脚本
整合所有模块，提供完整的训练和推理接口
"""

import argparse
import os
import json
import logging
from pathlib import Path
import torch
import sys

# 添加src目录到Python路径
sys.path.append("src")

from model_config import ModelConfig, create_training_arguments
from data_processor import create_data_loaders
from trainer import train_navigation_model, setup_swanlab, setup_wandb
from inference import NavigationInference

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


def parse_args():
    """解析命令行参数"""
    parser = argparse.ArgumentParser(description="Navigation Model Fine-tuning")

    # 模式选择
    parser.add_argument(
        "--mode",
        type=str,
        choices=["train", "inference", "evaluate"],
        default="train",
        help="运行模式",
    )

    # 数据相关
    parser.add_argument(
        "--data_root", type=str, default="data/finetune", help="数据根目录"
    )
    parser.add_argument(
        "--split_file",
        type=str,
        default="data/finetune/split.json",
        help="数据分割文件",
    )

    # 模型相关
    parser.add_argument(
        "--model_name",
        type=str,
        default="data/Qwen2.5-VL-3B-Instruct",
        help="基础模型名称或路径",
    )
    parser.add_argument(
        "--model_path", type=str, default=None, help="微调后的模型路径（用于推理）"
    )

    # 训练参数
    parser.add_argument("--output_dir", type=str, default="./results", help="输出目录")
    parser.add_argument("--num_train_epochs", type=int, default=3, help="训练轮数")
    parser.add_argument(
        "--per_device_train_batch_size", type=int, default=2, help="每设备训练批次大小"
    )
    parser.add_argument(
        "--per_device_eval_batch_size", type=int, default=2, help="每设备验证批次大小"
    )
    parser.add_argument(
        "--gradient_accumulation_steps", type=int, default=8, help="梯度累积步数"
    )
    parser.add_argument("--learning_rate", type=float, default=2e-4, help="学习率")
    parser.add_argument("--weight_decay", type=float, default=0.01, help="权重衰减")
    parser.add_argument("--warmup_ratio", type=float, default=0.1, help="预热比例")
    parser.add_argument("--max_length", type=int, default=512, help="最大序列长度")

    # LoRA参数
    parser.add_argument(
        "--use_lora", action="store_true", default=True, help="是否使用LoRA"
    )
    parser.add_argument("--lora_rank", type=int, default=16, help="LoRA rank")
    parser.add_argument("--lora_alpha", type=int, default=32, help="LoRA alpha")
    parser.add_argument("--lora_dropout", type=float, default=0.1, help="LoRA dropout")

    # 量化参数
    parser.add_argument(
        "--use_4bit", action="store_true", default=False, help="是否使用4bit量化"
    )
    parser.add_argument(
        "--no_4bit", action="store_true", default=False, help="禁用4bit量化"
    )

    # 其他参数
    parser.add_argument(
        "--resume_from_checkpoint", type=str, default=None, help="从检查点恢复训练"
    )
    parser.add_argument(
        "--use_swanlab", action="store_true", default=False, help="是否使用SwanLab记录"
    )
    parser.add_argument(
        "--swanlab_project",
        type=str,
        default="navigation-finetune",
        help="SwanLab项目名称",
    )
    # 保持向后兼容
    parser.add_argument(
        "--use_wandb",
        action="store_true",
        default=False,
        help="是否使用wandb记录（向后兼容，实际使用SwanLab）",
    )
    parser.add_argument(
        "--wandb_project",
        type=str,
        default="navigation-finetune",
        help="wandb项目名称（向后兼容，实际使用SwanLab）",
    )
    parser.add_argument("--run_name", type=str, default=None, help="运行名称")

    # 推理参数
    parser.add_argument("--image_path", type=str, default=None, help="推理时的图像路径")
    parser.add_argument("--question", type=str, default=None, help="推理时的问题")
    parser.add_argument("--eval_dataset", type=str, default=None, help="评估数据集路径")
    parser.add_argument(
        "--eval_output", type=str, default=None, help="评估结果输出路径"
    )

    return parser.parse_args()


def main():
    """主函数"""
    args = parse_args()

    # 创建输出目录
    os.makedirs(args.output_dir, exist_ok=True)

    # 保存配置
    config_path = os.path.join(args.output_dir, "config.json")
    with open(config_path, "w") as f:
        json.dump(vars(args), f, indent=2)

    logger.info(f"Configuration saved to: {config_path}")

    if args.mode == "train":
        train_model(args)
    elif args.mode == "inference":
        run_inference(args)
    elif args.mode == "evaluate":
        evaluate_model(args)
    else:
        raise ValueError(f"Unknown mode: {args.mode}")


def train_model(args):
    """训练模型"""
    logger.info("Starting model training...")

    # 设置SwanLab
    if args.use_swanlab or args.use_wandb:  # 支持两种参数
        project_name = (
            args.swanlab_project
            if hasattr(args, "swanlab_project")
            else args.wandb_project
        )
        setup_swanlab(project_name, args.run_name)

    # 创建模型配置
    use_4bit = args.use_4bit and not args.no_4bit  # 如果指定了no_4bit，则禁用4bit
    model_config = ModelConfig(
        model_name=args.model_name,
        use_4bit=use_4bit,
        use_lora=args.use_lora,
        lora_rank=args.lora_rank,
        lora_alpha=args.lora_alpha,
        lora_dropout=args.lora_dropout,
    )

    # 创建训练参数
    training_args = create_training_arguments(
        output_dir=args.output_dir,
        num_train_epochs=args.num_train_epochs,
        per_device_train_batch_size=args.per_device_train_batch_size,
        per_device_eval_batch_size=args.per_device_eval_batch_size,
        gradient_accumulation_steps=args.gradient_accumulation_steps,
        learning_rate=args.learning_rate,
        weight_decay=args.weight_decay,
        warmup_ratio=args.warmup_ratio,
        gradient_checkpointing=False,  # 禁用梯度检查点避免梯度问题
        report_to=[],  # 禁用内置日志，使用SwanLab回调
    )

    # 创建数据加载器
    from token_utils import NavigationTokenizer

    nav_tokenizer = NavigationTokenizer(args.model_name)
    processor = nav_tokenizer.setup_processor()

    train_loader, val_loader = create_data_loaders(
        data_root=args.data_root,
        split_file=args.split_file,
        processor=processor,
        batch_size=args.per_device_train_batch_size,
        max_length=args.max_length,
    )

    # 开始训练
    trainer, nav_model = train_navigation_model(
        model_config=model_config,
        training_args=training_args,
        train_dataset=train_loader.dataset,
        eval_dataset=val_loader.dataset,
        resume_from_checkpoint=args.resume_from_checkpoint,
    )

    logger.info("Training completed successfully!")


def run_inference(args):
    """运行推理"""
    if not args.model_path:
        raise ValueError("Model path is required for inference mode")

    if not args.image_path or not args.question:
        raise ValueError("Image path and question are required for inference mode")

    logger.info("Running inference...")

    # 创建推理器
    inference = NavigationInference(args.model_path)

    # 进行预测
    prediction = inference.predict(args.image_path, args.question)

    # 输出结果
    print("\n" + "=" * 50)
    print("INFERENCE RESULTS")
    print("=" * 50)
    print(f"Image: {args.image_path}")
    print(f"Question: {args.question}")
    print(f"Raw Output: {prediction['raw_output']}")
    print(f"Is Navigation: {prediction['is_navigation']}")
    print(f"Action: {prediction['action']}")
    print(f"Answer: {prediction['answer']}")
    print("=" * 50)


def evaluate_model(args):
    """评估模型"""
    if not args.model_path:
        raise ValueError("Model path is required for evaluation mode")

    if not args.eval_dataset:
        raise ValueError("Evaluation dataset is required for evaluation mode")

    logger.info("Running model evaluation...")

    # 创建推理器
    inference = NavigationInference(args.model_path)

    # 运行评估
    metrics = inference.evaluate_on_dataset(
        dataset_path=args.eval_dataset, output_path=args.eval_output
    )

    # 输出结果
    print("\n" + "=" * 50)
    print("EVALUATION RESULTS")
    print("=" * 50)
    for key, value in metrics.items():
        print(f"{key}: {value}")
    print("=" * 50)


if __name__ == "__main__":
    main()
