#!/usr/bin/env python3
"""
直接测试模型调用
"""

import torch
import sys

sys.path.append("src")

from model_config import NavigationModel, ModelConfig
from data_processor import NavigationDataset, collate_fn
from torch.utils.data import DataLoader


def test_model_direct():
    """直接测试模型调用"""
    print("=" * 50)
    print("直接测试模型调用")
    print("=" * 50)

    try:
        # 创建模型配置（不使用4bit量化）
        model_config = ModelConfig(
            model_name="data/Qwen2.5-VL-3B-Instruct", use_4bit=False, use_lora=True
        )

        # 初始化模型
        nav_model = NavigationModel(model_config)
        model = nav_model.get_model()
        tokenizer = nav_model.get_tokenizer()
        processor = nav_model.get_navigation_tokenizer().setup_processor()

        print(f"模型类型: {model.__class__.__name__}")

        # 检查PEFT模型的forward方法签名
        import inspect

        print(f"PEFT模型forward签名: {inspect.signature(model.forward)}")
        print(f"基础模型forward签名: {inspect.signature(model.base_model.forward)}")
        print(
            f"深层模型forward签名: {inspect.signature(model.base_model.model.forward)}"
        )

        # 创建测试数据
        dataset = NavigationDataset(
            data_root="data/finetune",
            split_file="data/finetune/split.json",
            split="train",
            processor=processor,
            max_length=512,
        )

        # 创建数据加载器
        dataloader = DataLoader(
            dataset, batch_size=1, collate_fn=collate_fn, shuffle=False
        )

        # 获取一个批次
        batch = next(iter(dataloader))

        print("批次键:", list(batch.keys()))
        for key, value in batch.items():
            if isinstance(value, torch.Tensor):
                print(f"{key}: shape={value.shape}")

        # 移动到设备
        device = model.device
        batch = {
            k: v.to(device) if isinstance(v, torch.Tensor) else v
            for k, v in batch.items()
        }

        print(f"\n模型设备: {device}")

        # 测试1：直接调用基础模型
        print("\n测试1：直接调用基础模型")
        try:
            base_model = model.base_model.model  # 获取最深层的模型
            print(f"基础模型类型: {base_model.__class__.__name__}")

            outputs = base_model(
                input_ids=batch["input_ids"],
                attention_mask=batch["attention_mask"],
                pixel_values=batch["pixel_values"],
                image_grid_thw=batch["image_grid_thw"],
            )
            print("✓ 成功调用基础模型")
            print(f"输出类型: {type(outputs)}")
            if hasattr(outputs, "last_hidden_state"):
                print(f"last_hidden_state shape: {outputs.last_hidden_state.shape}")
        except Exception as e:
            print(f"✗ 基础模型调用失败: {e}")
            import traceback

            traceback.print_exc()

        # 测试2：通过PEFT模型调用（不传递labels参数）
        print("\n测试2：通过PEFT模型调用（不传递labels参数）")
        try:
            outputs = model(
                input_ids=batch["input_ids"],
                attention_mask=batch["attention_mask"],
                pixel_values=batch["pixel_values"],
                image_grid_thw=batch["image_grid_thw"],
            )
            print("✓ 成功调用PEFT模型（不传递labels）")
            print(f"输出类型: {type(outputs)}")
            if hasattr(outputs, "last_hidden_state"):
                print(f"last_hidden_state shape: {outputs.last_hidden_state.shape}")
        except Exception as e:
            print(f"✗ PEFT模型调用失败: {e}")
            import traceback

            traceback.print_exc()

        # 测试2：传递labels参数
        print("\n测试2：传递labels参数")
        try:
            outputs = model(
                input_ids=batch["input_ids"],
                attention_mask=batch["attention_mask"],
                pixel_values=batch["pixel_values"],
                image_grid_thw=batch["image_grid_thw"],
                labels=batch["labels"],
            )
            print("✓ 成功调用模型（传递labels）")
        except Exception as e:
            print(f"✗ 调用失败: {e}")
            print("这是预期的错误，因为VL模型不支持labels参数")

        return True

    except Exception as e:
        print(f"测试失败: {e}")
        import traceback

        traceback.print_exc()
        return False


if __name__ == "__main__":
    test_model_direct()
