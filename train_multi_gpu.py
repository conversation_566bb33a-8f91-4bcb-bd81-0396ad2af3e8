#!/usr/bin/env python3
"""
多卡训练 Qwen2.5-VL 脚本
使用 Accelerate + DeepSpeed 实现8卡训练，充分利用所有显存
"""

import os
import sys
import argparse
import logging
import json
import torch
from pathlib import Path

# 添加src目录到Python路径
sys.path.append("src")

# 设置日志
logging.basicConfig(
    level=logging.INFO, format="%(asctime)s - %(name)s - %(levelname)s - %(message)s"
)
logger = logging.getLogger(__name__)


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="多卡训练 Qwen2.5-VL")

    # 数据参数
    parser.add_argument(
        "--data_root", type=str, default="data/finetune", help="数据根目录"
    )
    parser.add_argument(
        "--split_file",
        type=str,
        default="data/finetune/split.json",
        help="数据分割文件",
    )
    parser.add_argument(
        "--model_name",
        type=str,
        default="data/Qwen2.5-VL-3B-Instruct",
        help="模型名称或路径",
    )

    # 训练参数
    parser.add_argument(
        "--output_dir", type=str, default="results_multi_gpu", help="输出目录"
    )
    parser.add_argument("--num_train_epochs", type=int, default=1, help="训练轮数")
    parser.add_argument(
        "--per_device_train_batch_size", type=int, default=4, help="每设备训练批次大小"
    )
    parser.add_argument(
        "--gradient_accumulation_steps", type=int, default=2, help="梯度累积步数"
    )
    parser.add_argument("--learning_rate", type=float, default=2e-4, help="学习率")
    parser.add_argument("--max_length", type=int, default=2048, help="最大序列长度")
    parser.add_argument(
        "--max_samples", type=int, default=-1, help="最大训练样本数，-1表示使用全部数据"
    )
    parser.add_argument("--save_steps", type=int, default=500, help="保存检查点的步数")
    parser.add_argument("--logging_steps", type=int, default=10, help="日志记录步数")

    args = parser.parse_args()

    logger.info("开始多卡训练...")
    logger.info(f"参数: {args}")

    try:
        # 导入必要的库
        from transformers import (
            Qwen2_5_VLForConditionalGeneration,
            Qwen2VLProcessor,
            TrainingArguments,
            Trainer,
            DataCollatorForSeq2Seq,
        )
        from peft import LoraConfig, get_peft_model, TaskType
        from accelerate import Accelerator
        import torch.distributed as dist

        # 初始化 Accelerator
        accelerator = Accelerator()

        logger.info(f"使用设备: {accelerator.device}")
        logger.info(f"进程数: {accelerator.num_processes}")
        logger.info(f"本地进程数: {accelerator.local_process_index}")

        # 1. 加载模型和processor
        logger.info(f"加载模型: {args.model_name}")

        # 加载processor
        processor = Qwen2VLProcessor.from_pretrained(args.model_name)

        # 加载模型 - 使用更大的批次大小和优化设置
        model = Qwen2_5_VLForConditionalGeneration.from_pretrained(
            args.model_name,
            torch_dtype=torch.bfloat16,  # 使用bfloat16节省内存
            device_map=None,  # 让accelerator处理设备分配
            trust_remote_code=True,
            attn_implementation="eager",  # 避免flash attention问题
        )

        # 禁用缓存以支持训练
        model.config.use_cache = False

        logger.info("✅ 模型加载成功")

        # 2. 设置LoRA
        logger.info("设置LoRA适配器...")

        # 找到所有线性层作为目标模块
        target_modules = []
        for name, module in model.named_modules():
            if isinstance(module, torch.nn.Linear):
                target_modules.append(name.split(".")[-1])

        # 去重并过滤常见的线性层
        target_modules = list(set(target_modules))
        common_targets = [
            "q_proj",
            "k_proj",
            "v_proj",
            "o_proj",
            "gate_proj",
            "up_proj",
            "down_proj",
            "fc1",
            "fc2",
        ]
        target_modules = [t for t in target_modules if t in common_targets]

        logger.info(f"LoRA目标模块: {target_modules}")

        lora_config = LoraConfig(
            task_type=TaskType.CAUSAL_LM,
            r=32,  # 增加rank以提高性能
            lora_alpha=64,  # 增加alpha
            lora_dropout=0.1,
            target_modules=target_modules,
            bias="none",
        )

        model = get_peft_model(model, lora_config)
        model.print_trainable_parameters()

        logger.info("✅ LoRA设置完成")

        # 3. 准备数据
        logger.info("准备训练数据...")

        # 加载数据集
        from data_processor import NavigationDataset

        # 创建数据集
        dataset = NavigationDataset(
            data_root=args.data_root,
            split_file=args.split_file,
            split="train",
            processor=processor,
            max_length=args.max_length,
        )

        # 限制样本数量（如果指定）
        if args.max_samples > 0:
            dataset.data = dataset.data[: args.max_samples]
            logger.info(f"限制训练样本数量为: {args.max_samples}")

        logger.info(f"数据集大小: {len(dataset)}")

        # 4. 数据整理器 - 使用标准的DataCollatorForSeq2Seq
        from transformers import DataCollatorForSeq2Seq

        data_collator = DataCollatorForSeq2Seq(
            tokenizer=processor.tokenizer,
            model=model,
            padding=True,
            return_tensors="pt",
        )

        # 5. 训练参数
        # 计算总的有效批次大小
        total_batch_size = (
            args.per_device_train_batch_size
            * args.gradient_accumulation_steps
            * accelerator.num_processes
        )

        logger.info(f"每设备批次大小: {args.per_device_train_batch_size}")
        logger.info(f"梯度累积步数: {args.gradient_accumulation_steps}")
        logger.info(f"设备数量: {accelerator.num_processes}")
        logger.info(f"总有效批次大小: {total_batch_size}")

        training_args = TrainingArguments(
            output_dir=args.output_dir,
            num_train_epochs=args.num_train_epochs,
            per_device_train_batch_size=args.per_device_train_batch_size,
            gradient_accumulation_steps=args.gradient_accumulation_steps,
            learning_rate=args.learning_rate,
            weight_decay=0.01,
            warmup_ratio=0.03,
            lr_scheduler_type="cosine",
            logging_steps=args.logging_steps,
            save_steps=args.save_steps,
            save_total_limit=3,
            remove_unused_columns=False,
            dataloader_pin_memory=False,
            dataloader_num_workers=0,  # 禁用多进程避免pickle问题
            bf16=True,  # 使用bfloat16
            gradient_checkpointing=True,
            ddp_find_unused_parameters=False,
            report_to="none",
            seed=42,
        )

        # 6. 创建训练器
        trainer = Trainer(
            model=model,
            args=training_args,
            train_dataset=dataset,
            data_collator=data_collator,
            tokenizer=processor.tokenizer,  # 用于保存
        )

        logger.info("✅ 训练器创建完成")

        # 7. 显示内存使用情况
        if torch.cuda.is_available():
            for i in range(torch.cuda.device_count()):
                gpu_memory = torch.cuda.get_device_properties(i).total_memory / 1024**3
                logger.info(
                    f"GPU {i}: {torch.cuda.get_device_properties(i).name}, "
                    f"内存: {gpu_memory:.1f} GB"
                )

        # 8. 开始训练
        logger.info("🚀 开始多卡训练...")
        trainer.train()

        # 9. 保存模型
        logger.info("保存模型...")
        trainer.save_model()
        processor.save_pretrained(args.output_dir)

        logger.info("✅ 多卡训练完成！")
        logger.info(f"模型已保存到: {args.output_dir}")

    except Exception as e:
        logger.error(f"训练失败: {e}")
        import traceback

        traceback.print_exc()
        return 1

    return 0


if __name__ == "__main__":
    sys.exit(main())
