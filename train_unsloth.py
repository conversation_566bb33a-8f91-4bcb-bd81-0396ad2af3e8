#!/usr/bin/env python3
"""
基于 Unsloth 的 Qwen2.5-VL 加速训练脚本
"""

import os
import sys
import argparse
import logging
from pathlib import Path

# 添加src目录到Python路径
sys.path.append("src")

# 设置日志
logging.basicConfig(
    level=logging.INFO, format="%(asctime)s - %(name)s - %(levelname)s - %(message)s"
)
logger = logging.getLogger(__name__)


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="Unsloth 加速的 Qwen2.5-VL 微调")

    # 数据参数
    parser.add_argument(
        "--data_root", type=str, default="data/finetune", help="数据根目录"
    )
    parser.add_argument(
        "--split_file",
        type=str,
        default="data/finetune/split.json",
        help="数据分割文件",
    )
    parser.add_argument(
        "--model_name",
        type=str,
        default="data/Qwen2.5-VL-3B-Instruct",
        help="模型名称或路径",
    )

    # 训练参数
    parser.add_argument(
        "--output_dir", type=str, default="results_unsloth", help="输出目录"
    )
    parser.add_argument("--num_train_epochs", type=int, default=1, help="训练轮数")
    parser.add_argument(
        "--per_device_train_batch_size", type=int, default=2, help="每设备训练批次大小"
    )
    parser.add_argument(
        "--gradient_accumulation_steps", type=int, default=4, help="梯度累积步数"
    )
    parser.add_argument("--learning_rate", type=float, default=2e-4, help="学习率")
    parser.add_argument(
        "--max_steps", type=int, default=100, help="最大训练步数（用于快速测试）"
    )
    parser.add_argument("--max_length", type=int, default=2048, help="最大序列长度")
    parser.add_argument("--save_steps", type=int, default=500, help="保存检查点的步数")
    parser.add_argument("--logging_steps", type=int, default=10, help="日志记录步数")

    args = parser.parse_args()

    logger.info("开始 Unsloth 加速训练...")
    logger.info(f"参数: {args}")

    try:
        # 导入 Unsloth
        from unsloth import FastVisionModel, is_bf16_supported
        from unsloth.trainer import UnslothVisionDataCollator
        from trl import SFTTrainer, SFTConfig
        import torch

        logger.info("✅ Unsloth 导入成功")

        # 1. 加载模型和tokenizer
        logger.info(f"加载模型: {args.model_name}")
        model, tokenizer = FastVisionModel.from_pretrained(
            model_name=args.model_name,
            max_seq_length=args.max_length,
            dtype=None,  # 自动选择
            load_in_4bit=True,  # 使用4bit量化节省内存
        )

        logger.info("✅ 模型加载成功")

        # 2. 添加LoRA适配器
        model = FastVisionModel.get_peft_model(
            model,
            r=16,  # LoRA rank
            target_modules=[
                "q_proj",
                "k_proj",
                "v_proj",
                "o_proj",
                "gate_proj",
                "up_proj",
                "down_proj",
            ],
            lora_alpha=16,
            lora_dropout=0,  # 支持优化
            bias="none",  # 支持优化
            use_gradient_checkpointing="unsloth",  # 使用Unsloth的梯度检查点
            random_state=3407,
            use_rslora=False,  # 支持排名稳定的LoRA
            loftq_config=None,  # LoftQ
        )

        logger.info("✅ LoRA适配器添加成功")

        # 3. 准备数据
        logger.info("准备训练数据...")

        # 加载数据集
        from data_processor import NavigationDataset
        import json

        # 读取数据分割（如果需要的话）
        # with open(args.split_file, "r") as f:
        #     split_data = json.load(f)

        # 创建数据集（不使用processor，我们将使用Unsloth的方式）
        dataset = NavigationDataset(
            data_root=args.data_root,
            split_file=args.split_file,
            split="train",
            processor=None,  # 不使用processor
            max_length=args.max_length,
        )

        logger.info(f"数据集大小: {len(dataset)}")

        # 转换为Unsloth格式
        def convert_to_unsloth_format(sample):
            """转换为Unsloth期望的格式"""
            # 构建对话格式
            messages = [
                {
                    "role": "user",
                    "content": [
                        {"type": "image", "image": sample["image"]},
                        {"type": "text", "text": sample["input_text"]},
                    ],
                },
                {
                    "role": "assistant",
                    "content": [
                        {"type": "text", "text": sample["target"]},
                    ],
                },
            ]
            return {"messages": messages}

        # 转换数据集（使用全部样本）
        converted_dataset = []
        max_samples = len(dataset)  # 使用全部样本
        logger.info(f"转换全部 {max_samples} 个样本...")

        for i in range(max_samples):
            try:
                sample = dataset[i]
                converted_sample = convert_to_unsloth_format(sample)
                converted_dataset.append(converted_sample)

                if (i + 1) % 100 == 0:
                    logger.info(f"已转换 {i + 1}/{max_samples} 个样本")

            except Exception as e:
                logger.warning(f"跳过样本 {i}: {e}")
                continue

        logger.info(f"✅ 数据转换完成，共 {len(converted_dataset)} 个样本")

        # 4. 设置训练器
        logger.info("设置训练器...")

        # 启用训练模式
        FastVisionModel.for_training(model)

        trainer = SFTTrainer(
            model=model,
            tokenizer=tokenizer,
            data_collator=UnslothVisionDataCollator(model, tokenizer),  # 必须使用！
            train_dataset=converted_dataset,
            args=SFTConfig(
                per_device_train_batch_size=args.per_device_train_batch_size,
                gradient_accumulation_steps=args.gradient_accumulation_steps,
                warmup_steps=5,
                max_steps=args.max_steps,
                learning_rate=args.learning_rate,
                fp16=not is_bf16_supported(),
                bf16=is_bf16_supported(),
                logging_steps=args.logging_steps,
                save_steps=args.save_steps,
                optim="adamw_8bit",
                weight_decay=0.01,
                lr_scheduler_type="linear",
                seed=3407,
                output_dir=args.output_dir,
                report_to="none",  # 禁用wandb等
                # 视觉微调必需的设置
                remove_unused_columns=False,
                dataset_text_field="",
                dataset_kwargs={"skip_prepare_dataset": True},
                dataset_num_proc=4,
                max_seq_length=args.max_length,
            ),
        )

        logger.info("✅ 训练器设置完成")

        # 5. 显示内存使用情况
        if torch.cuda.is_available():
            gpu_stats = torch.cuda.get_device_properties(0)
            start_gpu_memory = round(
                torch.cuda.max_memory_reserved() / 1024 / 1024 / 1024, 3
            )
            max_memory = round(gpu_stats.total_memory / 1024 / 1024 / 1024, 3)
            logger.info(f"GPU: {gpu_stats.name}")
            logger.info(f"最大内存: {max_memory} GB")
            logger.info(f"已使用内存: {start_gpu_memory} GB")

        # 6. 开始训练
        logger.info("🚀 开始训练...")
        trainer_stats = trainer.train()

        # 7. 显示训练统计
        if torch.cuda.is_available():
            used_memory = round(
                torch.cuda.max_memory_reserved() / 1024 / 1024 / 1024, 3
            )
            used_memory_for_lora = round(used_memory - start_gpu_memory, 3)
            used_percentage = round(used_memory / max_memory * 100, 3)
            lora_percentage = round(used_memory_for_lora / max_memory * 100, 3)

            logger.info(f"训练时间: {trainer_stats.metrics['train_runtime']:.2f} 秒")
            logger.info(
                f"训练时间: {round(trainer_stats.metrics['train_runtime']/60, 2)} 分钟"
            )
            logger.info(f"峰值内存: {used_memory} GB")
            logger.info(f"训练增加内存: {used_memory_for_lora} GB")
            logger.info(f"内存使用率: {used_percentage}%")
            logger.info(f"训练内存使用率: {lora_percentage}%")

        # 8. 保存模型
        logger.info("保存模型...")
        model.save_pretrained(f"{args.output_dir}/lora_model")
        tokenizer.save_pretrained(f"{args.output_dir}/lora_model")

        logger.info("✅ 训练完成！")
        logger.info(f"模型已保存到: {args.output_dir}/lora_model")

    except Exception as e:
        logger.error(f"训练失败: {e}")
        import traceback

        traceback.print_exc()
        return 1

    return 0


if __name__ == "__main__":
    sys.exit(main())
