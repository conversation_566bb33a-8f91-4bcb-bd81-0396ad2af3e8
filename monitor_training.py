#!/usr/bin/env python3
"""
训练监控脚本
实时显示GPU使用情况、训练进度和性能指标
"""

import time
import subprocess
import psutil
import os
from datetime import datetime

def get_gpu_info():
    """获取GPU信息"""
    try:
        result = subprocess.run([
            'nvidia-smi', 
            '--query-gpu=index,name,memory.used,memory.total,utilization.gpu,temperature.gpu',
            '--format=csv,noheader,nounits'
        ], capture_output=True, text=True)
        
        if result.returncode == 0:
            lines = result.stdout.strip().split('\n')
            gpu_info = []
            for line in lines:
                parts = line.split(', ')
                if len(parts) >= 6:
                    gpu_info.append({
                        'index': int(parts[0]),
                        'name': parts[1],
                        'memory_used': int(parts[2]),
                        'memory_total': int(parts[3]),
                        'utilization': int(parts[4]),
                        'temperature': int(parts[5])
                    })
            return gpu_info
    except Exception as e:
        print(f"获取GPU信息失败: {e}")
    return []

def get_training_processes():
    """获取训练进程信息"""
    processes = []
    for proc in psutil.process_iter(['pid', 'name', 'cmdline', 'cpu_percent', 'memory_info']):
        try:
            if 'python' in proc.info['name'] and any('train' in arg for arg in proc.info['cmdline']):
                processes.append({
                    'pid': proc.info['pid'],
                    'name': proc.info['name'],
                    'cpu_percent': proc.info['cpu_percent'],
                    'memory_mb': proc.info['memory_info'].rss / 1024 / 1024
                })
        except (psutil.NoSuchProcess, psutil.AccessDenied):
            continue
    return processes

def format_memory(mb):
    """格式化内存显示"""
    if mb >= 1024:
        return f"{mb/1024:.1f}GB"
    return f"{mb:.0f}MB"

def print_status():
    """打印状态信息"""
    os.system('clear')  # 清屏
    
    print("=" * 80)
    print(f"🚀 Qwen2.5-VL 多卡训练监控 - {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("=" * 80)
    
    # GPU信息
    gpu_info = get_gpu_info()
    if gpu_info:
        print("\n📊 GPU 状态:")
        print("-" * 80)
        print(f"{'GPU':<4} {'名称':<20} {'显存使用':<12} {'利用率':<8} {'温度':<6}")
        print("-" * 80)
        
        total_memory_used = 0
        total_memory_total = 0
        
        for gpu in gpu_info:
            memory_percent = (gpu['memory_used'] / gpu['memory_total']) * 100
            total_memory_used += gpu['memory_used']
            total_memory_total += gpu['memory_total']
            
            print(f"GPU{gpu['index']:<3} {gpu['name']:<20} "
                  f"{format_memory(gpu['memory_used'])}/{format_memory(gpu['memory_total'])} ({memory_percent:.1f}%)"
                  f"{'':>2} {gpu['utilization']:>3}%{'':>3} {gpu['temperature']:>3}°C")
        
        print("-" * 80)
        total_percent = (total_memory_used / total_memory_total) * 100
        print(f"总计: {format_memory(total_memory_used)}/{format_memory(total_memory_total)} ({total_percent:.1f}%)")
    
    # 训练进程信息
    processes = get_training_processes()
    if processes:
        print("\n🔄 训练进程:")
        print("-" * 60)
        print(f"{'PID':<8} {'CPU%':<8} {'内存':<12} {'进程名'}")
        print("-" * 60)
        for proc in processes:
            print(f"{proc['pid']:<8} {proc['cpu_percent']:<8.1f} "
                  f"{format_memory(proc['memory_mb']):<12} {proc['name']}")
    
    # 系统资源
    cpu_percent = psutil.cpu_percent(interval=1)
    memory = psutil.virtual_memory()
    
    print(f"\n💻 系统资源:")
    print("-" * 40)
    print(f"CPU 使用率: {cpu_percent:.1f}%")
    print(f"内存使用: {format_memory(memory.used/1024/1024)}/{format_memory(memory.total/1024/1024)} ({memory.percent:.1f}%)")
    
    # 检查日志文件
    log_files = ['results_8gpu_full', 'results_max_memory', 'results_multi_gpu']
    print(f"\n📝 训练日志:")
    print("-" * 40)
    for log_dir in log_files:
        if os.path.exists(log_dir):
            log_file = os.path.join(log_dir, 'trainer_state.json')
            if os.path.exists(log_file):
                mtime = os.path.getmtime(log_file)
                mtime_str = datetime.fromtimestamp(mtime).strftime('%H:%M:%S')
                print(f"{log_dir}: 最后更新 {mtime_str}")
    
    print("\n" + "=" * 80)
    print("按 Ctrl+C 退出监控")

def main():
    """主函数"""
    print("启动训练监控...")
    
    try:
        while True:
            print_status()
            time.sleep(5)  # 每5秒更新一次
    except KeyboardInterrupt:
        print("\n监控已停止")

if __name__ == "__main__":
    main()
