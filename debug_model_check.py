#!/usr/bin/env python3
"""
调试模型检查逻辑
"""

import torch
import sys
sys.path.append("src")

from model_config import NavigationModel, ModelConfig
import inspect

def debug_model_check():
    """调试模型检查逻辑"""
    print("=" * 50)
    print("调试模型检查逻辑")
    print("=" * 50)
    
    try:
        # 创建模型配置（不使用4bit量化）
        model_config = ModelConfig(
            model_name="data/Qwen2.5-VL-3B-Instruct",
            use_4bit=False,
            use_lora=True
        )
        
        # 初始化模型
        nav_model = NavigationModel(model_config)
        model = nav_model.get_model()
        
        print(f"模型类型: {model.__class__.__name__}")
        
        # 检查是否有base_model
        if hasattr(model, 'base_model'):
            print(f"基础模型类型: {model.base_model.__class__.__name__}")
            
            # 检查基础模型的forward方法签名
            if hasattr(model.base_model, 'forward'):
                forward_signature = inspect.signature(model.base_model.forward)
                print(f"基础模型forward参数: {list(forward_signature.parameters.keys())}")
                print(f"是否支持labels: {'labels' in forward_signature.parameters}")
            
            # 检查更深层的模型
            if hasattr(model.base_model, 'model'):
                print(f"更深层模型类型: {model.base_model.model.__class__.__name__}")
                if hasattr(model.base_model.model, 'forward'):
                    deep_forward_signature = inspect.signature(model.base_model.model.forward)
                    print(f"深层模型forward参数: {list(deep_forward_signature.parameters.keys())}")
                    print(f"深层模型是否支持labels: {'labels' in deep_forward_signature.parameters}")
        
        # 检查模型的forward方法签名
        if hasattr(model, 'forward'):
            forward_signature = inspect.signature(model.forward)
            print(f"模型forward参数: {list(forward_signature.parameters.keys())}")
            print(f"模型是否支持labels: {'labels' in forward_signature.parameters}")
        
        return True
        
    except Exception as e:
        print(f"调试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    debug_model_check()
