#!/bin/bash

# 优化训练流程脚本
# 1. 预处理数据（一次性）
# 2. 快速启动训练

echo "🚀 优化的 Qwen2.5-VL 训练流程"
echo "================================"

# 配置参数
DATA_ROOT="data/finetune"
SPLIT_FILE="data/finetune/split.json"
MODEL_NAME="data/Qwen2.5-VL-3B-Instruct"
PREPROCESSED_DIR="data/preprocessed"
PREPROCESSED_FILE="$PREPROCESSED_DIR/train_data.pkl"

# 创建预处理目录
mkdir -p "$PREPROCESSED_DIR"

# 检查是否已有预处理数据
if [ -f "$PREPROCESSED_FILE" ]; then
    echo "✅ 发现已有预处理数据: $PREPROCESSED_FILE"
    
    # 显示数据信息
    if [ -f "${PREPROCESSED_FILE%.*}_metadata.json" ]; then
        echo "📊 数据集信息:"
        cat "${PREPROCESSED_FILE%.*}_metadata.json" | jq '.'
    fi
    
    read -p "是否重新预处理数据？(y/N): " -n 1 -r
    echo
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        REPROCESS=true
    else
        REPROCESS=false
    fi
else
    echo "❌ 未发现预处理数据，需要先进行预处理"
    REPROCESS=true
fi

# 预处理数据
if [ "$REPROCESS" = true ]; then
    echo ""
    echo "🔧 开始数据预处理..."
    echo "这是一次性操作，完成后可快速启动训练"
    
    # 选择预处理规模
    echo ""
    echo "选择预处理规模:"
    echo "1) 快速测试 (10,000 样本)"
    echo "2) 中等规模 (100,000 样本)"
    echo "3) 大规模 (500,000 样本)"
    echo "4) 全部数据 (795,436 样本)"
    
    read -p "请选择 (1-4): " -n 1 -r
    echo
    
    case $REPLY in
        1)
            MAX_SAMPLES=10000
            echo "选择: 快速测试 (10,000 样本)"
            ;;
        2)
            MAX_SAMPLES=100000
            echo "选择: 中等规模 (100,000 样本)"
            ;;
        3)
            MAX_SAMPLES=500000
            echo "选择: 大规模 (500,000 样本)"
            ;;
        4)
            MAX_SAMPLES=-1
            echo "选择: 全部数据 (795,436 样本)"
            ;;
        *)
            MAX_SAMPLES=10000
            echo "默认选择: 快速测试 (10,000 样本)"
            ;;
    esac
    
    echo ""
    echo "开始预处理 $MAX_SAMPLES 个样本..."
    
    python preprocess_data.py \
        --data_root "$DATA_ROOT" \
        --split_file "$SPLIT_FILE" \
        --model_name "$MODEL_NAME" \
        --output_file "$PREPROCESSED_FILE" \
        --max_samples $MAX_SAMPLES \
        --max_length 2048
    
    if [ $? -eq 0 ]; then
        echo "✅ 数据预处理完成！"
    else
        echo "❌ 数据预处理失败！"
        exit 1
    fi
fi

# 启动训练
echo ""
echo "🚀 启动优化训练..."

# 训练配置选择
echo ""
echo "选择训练配置:"
echo "1) 快速训练 (小批次，快速验证)"
echo "2) 标准训练 (平衡的批次大小)"
echo "3) 大批次训练 (充分利用显存)"

read -p "请选择 (1-3): " -n 1 -r
echo

case $REPLY in
    1)
        BATCH_SIZE=4
        GRAD_ACCUM=2
        MAX_STEPS=500
        echo "选择: 快速训练配置"
        ;;
    2)
        BATCH_SIZE=6
        GRAD_ACCUM=2
        MAX_STEPS=2000
        echo "选择: 标准训练配置"
        ;;
    3)
        BATCH_SIZE=8
        GRAD_ACCUM=4
        MAX_STEPS=-1
        echo "选择: 大批次训练配置"
        ;;
    *)
        BATCH_SIZE=6
        GRAD_ACCUM=2
        MAX_STEPS=1000
        echo "默认选择: 标准训练配置"
        ;;
esac

# 计算有效批次大小
EFFECTIVE_BATCH_SIZE=$((BATCH_SIZE * GRAD_ACCUM))
echo "有效批次大小: $EFFECTIVE_BATCH_SIZE"

# 启动训练
echo ""
echo "开始训练..."

python train_unsloth_optimized.py \
    --preprocessed_data "$PREPROCESSED_FILE" \
    --model_name "$MODEL_NAME" \
    --output_dir "results_optimized_$(date +%Y%m%d_%H%M%S)" \
    --num_train_epochs 1 \
    --per_device_train_batch_size $BATCH_SIZE \
    --gradient_accumulation_steps $GRAD_ACCUM \
    --learning_rate 2e-4 \
    --max_steps $MAX_STEPS \
    --save_steps 1000 \
    --logging_steps 50 \
    --max_samples -1

if [ $? -eq 0 ]; then
    echo ""
    echo "🎉 优化训练完成！"
    echo ""
    echo "📊 训练总结:"
    echo "- 使用预处理数据，避免重复转换"
    echo "- Unsloth 2x 加速训练"
    echo "- 优化的内存使用"
    echo "- 稳定的训练过程"
    echo ""
    echo "下次训练可以直接使用预处理数据，启动更快！"
else
    echo "❌ 训练失败！"
    exit 1
fi
