# 导航模型微调项目

基于 Qwen2.5-VL-3B 模型的视觉导航任务微调实现。

## 项目结构

```
finetune/
├── src/                          # 源代码目录
│   ├── data_processor.py         # 数据预处理模块
│   ├── token_utils.py            # 特殊Token处理
│   ├── model_config.py           # 模型配置和加载
│   ├── trainer.py                # 训练循环实现
│   └── inference.py              # 推理和测试模块
├── data/                         # 数据目录
│   ├── finetune/                 # 微调数据集
│   └── Qwen2.5-VL-3B-Instruct/   # 预训练模型
├── train.py                      # 主训练脚本
├── inference_demo.py             # 推理演示脚本
├── test_modules.py               # 模块测试脚本
├── config.yaml                   # 配置文件
└── README.md                     # 项目说明
```

## 环境设置

1. **激活 conda 环境**：

```bash
conda activate ./.conda
```

2. **安装依赖**（已完成）：

```bash
pip install torch torchvision torchaudio transformers datasets accelerate peft pillow numpy tqdm wandb deepspeed bitsandbytes
```

## 数据准备

确保数据结构如下：

```
data/finetune/
├── split.json                    # 数据分割文件
├── HM3D/                         # HM3D数据集
│   └── scene_id/
│       ├── scene_id.json         # 场景数据
│       └── *.jpg                 # 图像文件
└── Matterport/                   # Matterport数据集
    └── scene_id/
        ├── scene_id.json
        └── *.jpg
```

## 使用方法

### 1. 测试模块

首先运行测试脚本确保所有模块正常工作：

```bash
python test_modules.py
```

### 2. 训练模型

使用默认参数训练：

```bash
python train.py --mode train
```

使用自定义参数训练：

```bash
python train.py \
    --mode train \
    --data_root data/finetune \
    --model_name data/Qwen2.5-VL-3B-Instruct \
    --output_dir ./results \
    --num_train_epochs 3 \
    --per_device_train_batch_size 2 \
    --gradient_accumulation_steps 8 \
    --learning_rate 2e-4 \
    --use_lora \
    --use_4bit
```

启用 SwanLab 日志：

```bash
python train.py \
    --mode train \
    --use_swanlab \
    --swanlab_project navigation-finetune \
    --run_name my_experiment
```

或使用向后兼容的 wandb 参数（实际使用 SwanLab）：

```bash
python train.py \
    --mode train \
    --use_wandb \
    --wandb_project navigation-finetune \
    --run_name my_experiment
```

### 3. 推理测试

使用训练好的模型进行推理：

```bash
python inference_demo.py \
    --model_path ./results/final_model \
    --image_path path/to/image.jpg \
    --question "How do I get to the kitchen?"
```

或使用主脚本：

```bash
python train.py \
    --mode inference \
    --model_path ./results/final_model \
    --image_path path/to/image.jpg \
    --question "How do I get to the kitchen?"
```

### 4. 模型评估

在数据集上评估模型性能：

```bash
python train.py \
    --mode evaluate \
    --model_path ./results/final_model \
    --eval_dataset path/to/eval_dataset.json \
    --eval_output ./evaluation_results.json
```

## 配置说明

### 主要参数

- `--model_name`: 基础模型路径，默认为 `data/Qwen2.5-VL-3B-Instruct`
- `--data_root`: 数据根目录，默认为 `data/finetune`
- `--output_dir`: 输出目录，默认为 `./results`
- `--num_train_epochs`: 训练轮数，默认为 3
- `--per_device_train_batch_size`: 每设备批次大小，默认为 2
- `--gradient_accumulation_steps`: 梯度累积步数，默认为 8
- `--learning_rate`: 学习率，默认为 2e-4

### LoRA 参数

- `--use_lora`: 启用 LoRA 微调
- `--lora_rank`: LoRA rank，默认为 16
- `--lora_alpha`: LoRA alpha，默认为 32
- `--lora_dropout`: LoRA dropout，默认为 0.1

### 量化参数

- `--use_4bit`: 启用 4bit 量化以节省显存

## 特殊 Token

模型支持三个特殊的导航动作 token：

- `<move_forward>`: 向前移动
- `<turn_left>`: 向左转
- `<turn_right>`: 向右转

## 输出格式

### 训练输出

训练过程中会保存：

- 检查点文件（每 500 步）
- 最终模型（训练结束时）
- 训练日志和配置文件

### 推理输出

推理结果包含：

- `raw_output`: 模型原始输出
- `is_navigation`: 是否为导航动作
- `action`: 导航动作（如果适用）
- `answer`: 答案文本

## 故障排除

### 常见问题

1. **显存不足**：

   - 减小批次大小：`--per_device_train_batch_size 1`
   - 增加梯度累积：`--gradient_accumulation_steps 16`
   - 启用 4bit 量化：`--use_4bit`

2. **模型加载失败**：

   - 检查模型路径是否正确
   - 确保模型文件完整

3. **数据加载错误**：
   - 检查数据路径和文件结构
   - 验证 JSON 文件格式

### 调试模式

运行测试脚本进行调试：

```bash
python test_modules.py
```

## 性能优化

### 训练优化

1. **使用混合精度**：默认启用 bf16
2. **梯度检查点**：默认启用以节省显存
3. **LoRA 微调**：只训练少量参数
4. **4bit 量化**：进一步减少显存使用

### 推理优化

1. **批量推理**：使用`predict_batch`方法
2. **缓存模型**：避免重复加载
3. **优化生成参数**：调整`max_new_tokens`等

## 扩展功能

### 添加新的导航动作

1. 在`token_utils.py`中添加新 token
2. 更新数据处理逻辑
3. 重新训练模型

### 支持新的数据格式

1. 修改`data_processor.py`中的数据加载逻辑
2. 更新数据预处理流程

## 许可证

本项目基于原始 README.md 中的要求实现，用于学术研究目的。
